<?php

use App\Http\Controllers\AdvertisingController;
use App\Http\Controllers\AuthorController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EconomyController;
use App\Http\Controllers\ReaderController;
use App\Http\Controllers\ReviewController as AmazonReviewController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VaultController;
use App\Http\Controllers\NotificationController;
use App\Models\Book;
use Illuminate\Support\Facades\Route;
use <PERSON><PERSON>er<PERSON>n\CodeCoverage\Report\Html\Dashboard;
use App\Http\Controllers\ReviewVerificationController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\SystemBankController;
use App\Http\Controllers\SubscriptionController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Public routes
use App\Http\Controllers\HomeController;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

Route::get('/login', [UserController::class, 'loginView'])->name('login');
Route::get('/register', [UserController::class, 'registerView'])->name('register');

Route::post('/login', [UserController::class, 'loginUser'])->name('loginUser');
Route::post('/register', [UserController::class, 'registerUser'])->name('registerUser');

// Keep-alive route for session management
Route::get('/keep-alive', function () {
    return response()->json(['status' => 'alive']);
})->middleware('auth')->name('keep-alive');

// Google Authentication Routes
Route::get('auth/google', [App\Http\Controllers\GoogleAuthController::class, 'redirectToGoogle'])->name('login.google');
Route::get('auth/google/register', [App\Http\Controllers\GoogleAuthController::class, 'redirectToGoogleRegister'])->name('register.google');
Route::get('auth/google/callback', [App\Http\Controllers\GoogleAuthController::class, 'handleGoogleCallback'])->name('login.google.callback');

Route::get('/terms-n-conditions', function () {
    return view('terms');
})->name('terms');

// Subscription routes
Route::prefix('subscription')->name('subscription.')->group(function () {
    Route::get('/plans', [SubscriptionController::class, 'showPlans'])->name('plans');
    Route::get('/public-plans', [SubscriptionController::class, 'showPublicPlans'])->name('public-plans');
    Route::get('/success', [SubscriptionController::class, 'success'])->name('success');
    Route::get('/direct-checkout/{plan}', [SubscriptionController::class, 'directCheckout'])->name('direct-checkout');

    // Protected subscription routes
    Route::middleware(['auth'])->group(function () {
        Route::post('/cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('/sync', [SubscriptionController::class, 'syncSubscription'])->name('sync');
        Route::get('/update-payment/{subscription_id}', [SubscriptionController::class, 'getUpdatePaymentTransaction'])->name('update-payment');
    });
});

// Protected routes - require authentication
Route::middleware(['auth', 'session.timeout', 'last.activity', 'subscription.flow'])->group(function () {
    Route::get('/dashboard', [AuthorController::class, 'returnAuthorPage'])->name('dashboard');
    Route::get('/logout', [UserController::class, 'logout'])->name('Log Out');
    Route::get('/author', [AuthorController::class, 'returnAuthorPage'])->name('author');
    Route::get('/reader', [ReaderController::class, 'returnReaderPage'])->name('reader');

    // Profile routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'index'])->name('profile.index');
    Route::post('/profile/update', [App\Http\Controllers\ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('/profile/amazon-reviewer', [App\Http\Controllers\ProfileController::class, 'updateAmazonReviewerName'])->name('profile.amazon_reviewer');
    Route::post('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');

    Route::get('/review/{id}/{slug}', [ReaderController::class, 'reviewPage'])->name('reviewPage');
    Route::get('/vault', [VaultController::class, 'returnVaultPage'])->name('vault');
    Route::get('/book-create', [BookController::class, 'viewCreateBook'])->name('book.create');
    Route::post('/book-create', [BookController::class, 'createBook'])->name('book.store');
    Route::get('/book/{slug}', [BookController::class, 'showSingleBook'])->name('books.single');
    Route::post('/post-quiz-answer', [ReaderController::class, 'postQuizAnswer'])->name('postQuizAnswer');
    Route::post('/boost-book', [AdvertisingController::class, 'boostBook'])->name('boost.book');
    Route::get('/view-book/{amount}/{slug}', [VaultController::class, 'getSingleBook'])->name('vault.singleBook');
    Route::get('/view-book-reader/{slug}', [ReaderController::class, 'getSingleBook'])->name('reader.singleBook');
    Route::post('/approve-book/{id}', [BookController::class, 'approve'])->name('approve-book');
    Route::post('book-assigned/{amount}/{slug}', [ReaderController::class, 'read'])->name('book.assign');
    Route::get('book-author/edit/{slug}', [AuthorController::class, 'returnAuthorEditPage'])->name('authorEdit.single');
    Route::post('book-author/edit-approve/{id}', [AuthorController::class, 'editBook'])->name('edit-book.author');
    Route::post('review', [ReviewController::class, 'getReview'])->name('post.review');
    Route::post('/cancelbook/{id}', [ReaderController::class, 'cancelAssignment'])->name('cancelAssignment');
    Route::get('/author-dashboard', [AuthorController::class, 'getAuthorDashbaord'])->name('AuthorDashbaord');
    Route::post('/filter-books', [VaultController::class, 'filterBooks'])->name('filter.books');
Route::post('/mark-notifications-read', [NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
Route::post('/notifications/mark-as-read-ajax', [NotificationController::class, 'markAllAsReadAjax'])->name('notifications.markAllAsReadAjax');

    // Notice tipping route
    Route::post('/tip-notice', [SystemBankController::class, 'tipNotice'])->name('tip.notice');

    // Feedback routes
    Route::post('/feedback/submit', [App\Http\Controllers\FeedbackController::class, 'submitFeedback'])->name('feedback.submit');

    // Subscription routes (that require authentication)
    Route::get('/subscription/plans', [App\Http\Controllers\SubscriptionController::class, 'showPlans'])->name('subscription.plans');
});

// Subscription success route - accessible without authentication
Route::get('/subscription/success', [App\Http\Controllers\SubscriptionController::class, 'success'])->name('subscription.success');

// Paddle webhook - no auth required and no CSRF protection
Route::post('/webhook/paddle', [App\Http\Controllers\SubscriptionController::class, 'webhook'])
    ->name('webhook.paddle')
    ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);

// Test webhook route - use this for testing webhook connectivity with curl or Postman
Route::post('/webhook/test', [App\Http\Controllers\SubscriptionController::class, 'webhookTest'])->name('webhook.test');

// Paddle.js test page - accessible without authentication
Route::get('/paddle-test', function() {
    return view('paddle-test');
})->name('paddle.test');

// Paddle debug page - accessible without authentication
Route::get('/paddle-debug', function() {
    return view('paddle-debug');
})->name('paddle.debug');


// Admin routes
Route::middleware(['auth', 'session.timeout', 'last.activity', 'admin'])->group(function () {
    Route::get('/admin-login', [DashboardController::class, 'returnAllBooks'])->name('allBooksDashboard');
    Route::get('/admin-review', [DashboardController::class, 'returnReviews'])->name('reviewDashboard');
    Route::get('/admin-all-reviews', [DashboardController::class, 'returnAllReviews'])->name('allReviewsDashboard');
    Route::get('/admin-approval', [DashboardController::class, 'returnApprovals'])->name('approvalDashboard');
    Route::post('/accept-book/{id}', [DashboardController::class, 'approve'])->name('accept-book');
    Route::get('admin-login/edit-single-book/{slug}', [DashboardController::class, 'returnSingleBook'])->name('edit.singleBook');
    Route::post('admin/book/edit/{id}', [DashboardController::class, 'UpdateAdminBoook'])->name('admin.book.edit');
    Route::post('/update-review-status/{id}', [DashboardController::class, 'updateReviewStatus'])->name('updateReviewStatus');
    Route::post('/update-partial-points/{id}', [DashboardController::class, 'updatePartialPoints'])->name('partialUpdatePoints');
    Route::post('reject-review/{id}', [DashboardController::class, 'RejectedReviewPoints'])->name('RejectedReviewPoints');
    Route::get('/admin-quiz-layers', [DashboardController::class, 'getAdminQuizLayers'])->name('AdminQuizLayers');
    Route::get('/admin-quiz-details/{bookId}', [DashboardController::class, 'getAdminQuizDetails'])->name('AdminQuizDetails');
    Route::get('/admin-book-economy', [EconomyController::class, 'returnBookEconomy'])->name('bookEconomy');
    Route::post('/admin-book-economy/TURNAROUND', [EconomyController::class, 'getTurnAroundTime'])->name('getTurnAroundTime');
    Route::post('/admin-book-economy/Percentage', [EconomyController::class, 'getQuestionPercentage'])->name('getPercentage');
    Route::post('/admin-book-economy/update', [EconomyController::class, 'updateBookEconomy'])->name('updateBookEconomy');
    Route::get('/admin-book-economy/check-flow-status', [EconomyController::class, 'checkFlowStatus'])->name('checkFlowStatus');

    Route::get('/admin/book/edit/{slug}', [DashboardController::class, 'getUpdatedDashboard'])->name('getAdminDashboard');
    Route::post('/request-modification', [DashboardController::class, 'requestModification'])->name('requestModification');
    Route::post('/request-new-modification', [DashboardController::class, 'requestNewModification'])->name('RequestNewModification');

    // Auto-check review route
    Route::post('/auto-check-review/{id}', [ReviewVerificationController::class, 'autoCheckReview'])->name('autoCheckReview');

    // System Bank routes
    Route::get('/admin-system-bank', [SystemBankController::class, 'index'])->name('admin.systemBank');
    Route::post('/admin-system-bank/update', [SystemBankController::class, 'updateSystemBank'])->name('admin.systemBank.update');

    // Admin Feedback routes
    Route::get('/admin-feedback', [App\Http\Controllers\FeedbackController::class, 'adminFeedbackDashboard'])->name('admin.feedback');
    Route::post('/admin-feedback/send-rewards', [App\Http\Controllers\FeedbackController::class, 'sendFeedbackRewards'])->name('admin.feedback.sendRewards');
});

Route::get('/thank-you', function () {
    return view('accomodities.thank-you');
})->name('thank-you');

Route::get('/test', [VaultController::class, 'testFunction']);

// TEMPORARY: Test route to simulate pending review scenario (REMOVE AFTER TESTING)
Route::get('/test-pending-scenario', function() {
    if (Auth::check()) {
        $user = Auth::user();

        // Set the waiting flag
        $user->waiting_for_first_review_approval = true;
        $user->save();

        // Set all user's reviews to pending status (0) to simulate waiting for approval
        \App\Models\Review::where('userid', $user->id)->update(['reviewStatus' => 0]);

        return "✅ User " . $user->email . " is now simulating pending review approval scenario. <a href='/vault'>Test Vault Access</a>";
    }
    return "Not logged in";
})->middleware('auth');

// TEMPORARY: Test route to restore normal access (REMOVE AFTER TESTING)
Route::get('/test-restore-access', function() {
    if (Auth::check()) {
        $user = Auth::user();

        // Clear the waiting flag
        $user->waiting_for_first_review_approval = false;
        $user->save();

        // Restore reviews to approved status
        \App\Models\Review::where('userid', $user->id)->update(['reviewStatus' => 1]);

        return "✅ User " . $user->email . " access restored. <a href='/vault'>Test Vault Access</a>";
    }
    return "Not logged in";
})->middleware('auth');





Route::get('/api/hero-images', [App\Http\Controllers\HomeController::class, 'getHeroImages'])->name('hero.images');

Route::get('/api/random-image', function (Illuminate\Http\Request $request) {
    $folder = $request->query('folder', '1.Horizontal');
    $image = App\Helpers\ImageHelper::getRandomImage($folder);
    return response()->json($image);
});

// Admin setup routes
Route::get('/admin-setup-form', [App\Http\Controllers\AdminSetupController::class, 'showSetupForm'])->name('admin.setup.form');
Route::get('/admin-setup-simple', function() {
    return view('admin.admin-simple-setup');
})->name('admin.setup.simple');
Route::post('/admin-setup', [App\Http\Controllers\AdminSetupController::class, 'setAdmin'])->name('admin.setup');
Route::post('/check-admin', [App\Http\Controllers\AdminSetupController::class, 'checkAdmin'])->name('admin.check');
Route::get('/direct-admin-check', [App\Http\Controllers\AdminSetupController::class, 'directAdminCheck'])->name('admin.direct.check');

// Check review on Amazon
Route::get('/reviews/check/{id}', [AmazonReviewController::class, 'checkReviewAmazon'])->name('reviews.check');

// Paddle webhook route is defined above
