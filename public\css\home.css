/* General Styles */
/* Define CSS variables for reuse */
:root {
    /* Color variables are now dynamically generated in the header */
    --text-color: #333; /* Dark gray */
    --light-gray: #f3f3f3; /* Light gray */
}

/* Body styles */
body {
    font-family: "Lato";
    color: var(--text-color);
    line-height: 1.4;
}

/* Utility Classes */
.lead {
    font-family: "Lato";
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-color);
    font-weight: 300;
    margin: 0;
}


.emphasized-text {
    font-size: 1.0rem; /* Increase font size */
    font-weight: 300; 
    }

.emphasized-accent-text {
    font-size: 0.9rem; /* Increase font size */
    font-weight: 300; 
    color: var(--accent-color) !important;
}

.accent-text {
    color: var(--accent-color);
}

.accent-background-text {
    background-color: var(--accent-color);
    color: white !important;
    padding: 0.2em 0.4em;
    border-radius: 0.2em;
    white-space: nowrap;
}

.highlighted-title {
    color: var(--accent-color) !important; /* Keep !important for utility */
}

.primary-color {
    color: var(--primary-color) !important; /* Keep !important for utility */
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.5rem 1.5rem;
}

/* Navigation */
.navbar {
    padding: 1rem 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-weight: 100;
    font-size: 1.0rem;
}

.navbar-brand {
    font-size: 1.6rem;
    color: var(--primary-color);
}

/* Base nav link styles */
.nav-link {
    color: var(--text-color);
    margin: 0 1.5rem;
    text-decoration: none; /* Ensure no underline by default */
}

/* Guest/Desktop nav link styles */
.nav-container ul li a,
.deskHEader .nav-container ul li a {
    font-weight: 300;
    font-size: 1.0rem;
    color: var(--text-color); /* Redundant, but keep for safety */
    text-decoration: none; /* Redundant, but keep for safety */
}

/* Mobile nav link styles */
.mobmenu .nav-container ul li a {
    font-weight: 400;
    font-size: 1.9rem;
    color: var(--text-color); /* Redundant, but keep for safety */
    text-decoration: none; /* Redundant, but keep for safety */
}

/* Navigation hover effects - Consolidate and remove !important if possible */
/* Check specificity - .nav-container ul li a is more specific than .nav-link */
.nav-container ul li a:hover,
.mobmenu .nav-container ul li a:hover {
    color: var(--accent-color); /* Removed !important, relying on specificity */
}


/* Dropdown Menu Styles (Generic) */
/* These seem to be generic styles, potentially for Bootstrap dropdowns */
.dropdown-menu {
    display: none;
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 150px;
    padding: 8px 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    padding: 8px 15px;
    color: var(--text-color);
    text-decoration: none;
    display: block;
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
    color: var(--accent-color);
}


/* User Dropdown Styles */
#userTrigger {
    position: relative;
    cursor: pointer;
    display: inline-block;
}

#userTrigger > a {
    display: inline-block;
    text-decoration: none;
    color: var(--text-color);
    padding: 0;
}

#userDropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 150px;
    padding: 8px 0;
    margin-top: 5px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    transform-origin: top right;
}

#userDropdown.active {
    display: block;
    opacity: 1;
    visibility: visible;
    height: auto;
    width: auto;
}

#userDropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    display: block;
}

#userDropdown ul li {
    padding: 8px 15px;
    width: 100%;
    box-sizing: border-box;
    display: block;
    height: auto;
}

#userDropdown ul li:hover {
    background-color: #f5f5f5;
}

#userDropdown ul li a {
    color: var(--text-color);
    text-decoration: none;
    display: block;
    width: 100%;
    font-size: 14px;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    padding: 0;
    height: auto;
    line-height: normal;
}

#userDropdown ul li a:hover {
    color: var(--accent-color);
}

/* Notification Styles */
.header_notification {
    position: relative;
}

#showNotification {
    cursor: pointer;
}

.notification_container {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.notification_container.active {
    display: block;
}


/* Quote Section */
.quote-container {
    padding: 2rem 0;
    background: white;
}

.literary-quote {
    font-style: italic;
    font-size: 1.2rem;
    color: var(--primary-color);
    margin: 0;
}

/* Hero Section */


.hero-image-container {
    position: relative;
    margin-bottom: 4rem;
    transition: opacity 0.5s ease;
}

.hero-image-container img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 300;
}

.hero-content {
     transition: opacity 0.5s ease;
}

/* Move the "Join BETA" button under the hero image */
.hero-image-container .hero-cta {
    position: absolute;
    bottom: -4rem;
    width: 100%;
    text-align: center;
    z-index: 10;
}

.hero-content .hero-cta {
    display: none; /* Hide the original button */
}



/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Section Titles and Headings */
/* Base styles for section titles */
.section-title,
.heading {
    display: inline-block;
    position: relative;
    font-size: 1.8rem; /* Base font size */
    line-height: 1.2;
    background: white;
    padding: 0 20px 10px; /* Adjusted padding */
    margin: 0;
    max-width: 90%;
    width: auto;
    text-align: center;
    border-bottom: 1px solid #ccc; /* Base border */
    font-weight: 300;
    color: inherit;
    z-index: 1; /* Ensure text is above the line */
}

/* Specific style for the main title container heading */
.title-container .heading {
    font-size: 2.0rem; /* Larger font size for main heading */
    line-height: 1.4;
    padding: 0 20px; /* Different padding */
    border-bottom: none; /* No border for main heading */
    width: auto; /* Redundant, but keep */
    max-width: none; /* Ensure it doesn't get max-width from .heading */
}

/* Container for titles with the horizontal line */
.title-container {
    text-align: center;
    margin: 0 0 120px; /* Increased bottom margin */
    display: flex;
    justify-content: center;
    position: relative;
}

/* Horizontal line for title container */
.title-container::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #eeeeee; /* Changed to #eee */
    z-index: 0;
}

/* Remove underline for specific section titles */
.title-container .heading::after {
    display: none;
}

/* Specific overrides for features and how section titles */
#features .section-title,
#how .section-title {
    font-size: 1.5rem; /* Smaller font size */
    max-width: 90%; /* Redundant, but keep */
    width: auto; /* Redundant, but keep */
}

/* Section Subtitles */
.section-subtitle,
.hero-content h2,
.differs-content h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    font-weight: 300;
    line-height: 1.4;
}

/* Subimage Subtitles */
.work-box-title {
    color: var(--primary-color) !important; /* Keep !important as it was in original */
    font-size: 1rem;
    font-weight: 400 !important;
    margin-bottom: 0.5rem;
}

/* Container centering override - Consolidate and remove !important if possible */
/* These selectors are specific enough, !important might not be needed */
#what .container,
#how .container,
#features .container,
#differs .container {
    text-align: center;
    position: relative;
}

/* Remove old line styles */
#what .container::after,
#how .container::after,
#features .container::after,
#differs .container::after {
    display: none;
}

/* Add more space under section titles / Override Bootstrap's mt-5 - Remove !important if possible */
/* These are likely needed to override Bootstrap's default margins */
.title-container + .row {
    margin-top: 0 !important; /* Keep !important */
}

#what .row.mt-5,
#how .row.mt-5,
#features .row.mt-5,
#differs .row.mt-5 {
    margin-top: 0 !important; /* Keep !important */
}

/* Work Boxes */
.work-box {
    display: flex;
    flex-direction: column;
    height: 450px;
    margin-bottom: 2rem;
    background: white;
    border: none;
    box-shadow: none;
    position: relative;
    overflow: visible;
}

.work-box-image-container {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
    margin-bottom: 1rem;
}

.work-box-image-container img {
    height: 100%;
    width: auto;
    max-width: none;
    object-fit: cover;
    object-position: center;
    position: relative;
    z-index: 1;
}

/* Row and Column Spacing Override */
.row {
    margin-left: -10px;
    margin-right: -10px;
}

.col-md-3 {
    padding-left: 10px;
    padding-right: 10px;
}

.work-box-content {
    padding: 0.5rem 0;
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.work-box-description {
    font-family: "Lato";
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
    font-weight: 300;
}

.work-box-description-container {
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
    transition: height 0.3s ease;
}

.truncated-description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 4.5em; /* 3 lines x 1.5 line-height */
    line-clamp: 3;
}

/* Expanded state */
.work-box-description-container.expanded {
    max-height: none;
    -webkit-line-clamp: unset;
    line-clamp: unset;
    height: auto !important; /* Keep !important as it was in original */
}

.learn-more-content {
    font-family: "Lato";
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
    font-weight: 300;
    margin-bottom: 0.5rem;
    background-color: transparent; /* Redundant, but keep */
}

.learn-more-link {
    color: #4e7ac7;
    text-decoration: underline;
    cursor: pointer;
    font-family: "Lato";
    font-weight: 300;
}

/* Dropdown styling to match description (within work box) */
.work-box .dropdown {
    margin-top: 0.5rem;
}

.work-box .dropdown-menu {
    min-width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: left;
    max-width: 100%;
    width: 100%;
    margin-top: 0;
}

.work-box .dropdown-item {
    padding: 0;
    white-space: normal;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    display: block;
    text-decoration: none;
    background: none;
}

.work-box .dropdown-item:hover {
    background: none;
    color: #666;
}

.work-box .dropdown-toggle {
    color: #4e7ac7;
    text-decoration: none;
    background: none;
    border: none;
    padding: 0;
    font-size: 0.9rem;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

.work-box .dropdown-toggle:hover {
    text-decoration: underline;
}

/* Image caption that matches image width (within work box) */
.work-box .image-caption {
    position: absolute;
    bottom: 0;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 300;
    z-index: 2;
    width: fit-content;
    white-space: nowrap;
    left: 50%;
    max-width: 100%;
}

/* Large number overlay */
.work-box-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    z-index: 1;
    pointer-events: none;
}

/* Features Section */
.feature-box {
    background: white;
    border-radius: 0;
    overflow: hidden;
    height: 100%;
    box-shadow: none;
    margin-bottom: 1rem;
}

.feature-box img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.feature-content {
    padding: 0.5rem 0;
}

.feature-content h4 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 400;
}

.feature-content p {
    font-size: 0.8rem;
    color: var(--text-color);
}

/* Testimonials Section - Hide completely */
#testimonials,
section#testimonials,
.review-item,
.testimonial-swiper {
    display: none !important; /* Keep !important as it was in original */
}

/* FAQs Section */
.accordion-button {
    color: var(--primary-color);
    background-color: #f8f9fa;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

/* Accordion button styling */
.accordion-button.collapsed {
    background-color: #f8f9fa;
}

/* Chat Now Section */
.chat-now-container {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 8px;
    text-align: center;
}

.chat-now-container h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Plans Section */
.pricing-box {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    height: 100%;
}


.pricing-title h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}
.pricing-features,
.pricing-features ul {
    list-style: none;
    padding: 0;
    font-weight: 300;
    font-size: 0.9rem;
    color: #333;
    text-align: left;
    line-height: 1.4;
}

.pricing-features li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.pricing-features li:before {
    content: "✓";
    color: var(--accent-color);
    position: absolute;
    left: 0;
}

.pricing-box.featured {
    border: 2px solid var(--accent-color);
}

/* How AUTEURS.SPACE differs Section */
.differs-image-container {
    position: relative;
    margin-bottom: 2rem;
    max-height: 500px;
    overflow: hidden;
    margin-right: 0; /* Ensure no extra right margin */
}

.differs-image-container img {
    max-width: 250px;
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    margin: 0 auto;
    display: block;
}

.differs-row {
    display: flex;
    align-items: flex-start;
    margin-left: 0;
    margin-right: 0;
}

.differs-image-col {
    padding-right: 0;
}

.differs-content-col {
    padding-left: 0;
    margin-left: 0px;
    text-align: left;
}

.differs-content {
    padding-top: 0;
    padding-left: 0;
}

.differs-content h2 {
    margin-top: 0;
    margin-bottom: 5px;
    text-align: left;
}

.differs-content h2:not(:first-child) {
    margin-top: 1rem !important; /* Keep !important as it was in original */
}

.differs-content {
    font-weight: 300;
    font-size: 0.9rem;
    color: #333;
    text-align: left;
    line-height: 1.4;
}

/* Add media query for mobile devices */
@media (max-width: 767px) {
    .differs-content-col {
        text-align: center;
    }
    
    .differs-content {
        text-align: center;
    }
    
    .differs-content h2 {
        text-align: center;
    }
    
    /* Ensure paragraphs are also center-aligned */
    .differs-content p {
        text-align: center;
    }
      
        /* Center-align bullet points with text */
        .differs-content ul {
            display: inline-block;
            text-align: left;
            margin: 0 auto;
            padding-left: 20px;
        }
        
        /* Adjust bullet point spacing */
        .differs-content li {
            text-align: left;
            margin-bottom: 0.5rem;
        }
}


/* Reduce space below title in differs section */
#differs .title-container {
    margin-bottom: 5rem !important; /* Keep !important as it was in original */
}

/* "Why It Exists" text blocks and lists */
#what .col-md-5 section p,
#what .col-md-5 section li,
#how .col-md-5 section li {
    font-weight: 300;
    font-size: 0.9rem;
    color: #333;
    text-align: left;
    line-height: 1.4;
}

#what .col-md-5 section ul,
#how .col-md-5 section ul {
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 1rem;
}

/* Style for h3 with highlighted-title class */
h3.highlighted-title {
    font-size: 1.3rem;
    font-weight: 300;
    color: var(--accent-color);
    margin-bottom: 1rem;
    margin-top: 2rem;
}

/* Custom margin for the "What is" section's central image */
.what-is-image-section {
    margin-top: 5rem;
    margin-bottom: 5rem;
}

/* Add margin below the "This Is Not Another Review Exchange" text block */
#what .col-md-8 > h2.heading + div.lead {
    margin-bottom: 5rem;
}

/* Rules list items */
#what .rules-list li,
.rules-list li {
    line-height: 1.4 !important; 
    margin-bottom: 0.8rem; /* Increased from 0.5rem to 0.8rem for more spacing */
}

.how-rules-list li {
    line-height: 1.4 !important; 
    margin-bottom: 0.8rem; /* Increased from 0.5rem to 0.8rem for more spacing */
    /*text-align: left !important;*/
}

.how-rules-list {
    list-style-position: inside;
    /* Additional padding adjustments might be needed */
}


/* Parallax Section Styles */
.auteurs-screens-section {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0 auto; /* Center the section */
}

.background-html-parallax {
    transition: transform 0.1s ease-out;
}

.background-html-parallax .notes-container,
.background-html-parallax .quiz-results-section,
.background-html-parallax .pre-quiz-section,
.background-html-parallax .vault-section,
.background-html-parallax .section-title,
.background-html-parallax .activity-heading {
    transition: transform 0.1s ease-out;
}

/* Parallax section styles */
.auteurs-screens-section.background-html-parallax {
    height: 350px; /* Fixed height for parallax sections */
}

/* Remove box-shadows from all parallax sections */
.auteurs-screens-section.background-html-parallax .container,
.auteurs-screens-section.background-html-parallax .note-card,
.auteurs-screens-section.background-html-parallax .reader-padding-medium .container,
.auteurs-screens-section.background-html-parallax .pre-quiz-section .container,
.auteurs-screens-section.background-html-parallax .quiz-results-section .container {
    box-shadow: none !important;
}

.auteurs-screens-section[style*="background-image"] {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
}

.content-overlay {
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

/* Parallax section sizing with content scaling */
.auteurs-screens-section .container {
    width: 80%;
    max-width: 800px !important; /* Keep !important as it was in original */
    padding: 15px; /* Add padding consistent with other containers */
    border-radius: 5px; /* Add border-radius consistent with other containers */
    background-color: transparent !important; /* Make background transparent */
    margin-bottom: 1rem; /* Add bottom margin consistent with other containers */
    box-shadow: none !important; /* Ensure no box-shadow */
}

/* Remove or override any reader-padding-medium styles */
.auteurs-screens-section .reader-padding-medium {
    padding: 0 !important; /* Keep !important as it was in original */
    margin: 0 auto; /* Center the content */
    padding-top: 1rem; /* Add top padding */
}

/* Ensure all parallax section titles are properly centered and responsive */
.auteurs-screens-section .section-title,
.auteurs-screens-section .activity-heading {
    display: block;
    margin: 0 auto 2rem;
    text-align: center;
    font-size: 1.5rem;
    max-width: 100%; /* Changed from 90% to 100% */
    width: 100% !important; /* Changed from auto to 100% */
    padding-top: 0;
    clear: both;
    transition: transform 0.1s ease-out;
}

/* Specific styles for sections within parallax */
.auteurs-screens-section .notes-section {
    width: 100%;
    max-width: 1000px; /* Keep as it was in original */
    margin: 0 auto;
    padding-top: 0rem;
    position: relative;
}

.auteurs-screens-section .note-card {
    background: #fff !important; /* Keep !important as it was in original */
    width: 100%;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.125) !important; /* Keep !important as it was in original */
    box-shadow: none !important; /* Ensure no box-shadow */
}

.auteurs-screens-section .activity-heading {
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-weight: 300;
}

.auteurs-screens-section .quiz-results-section {
    width: 100%;
    max-width: 1000px; /* Keep as it was in original */
    margin: 0 auto;
    padding-top: 5rem;
}

.auteurs-screens-section .quiz-header {
    font-size: 15px;
    font-weight: 300;
    margin-bottom: 15px;
    color: #666 !important; /* Keep !important as it was in original */
}

.auteurs-screens-section .quiz-results-section .quiz-results-card {
    background-color: white !important; /* Change to white */
    border: 1px solid rgba(0, 0, 0, 0.125) !important; /* Keep !important as it was in original */
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 1rem;
}

.auteurs-screens-section .quiz-results-section .quiz-results {
    margin-top: 15px;
    padding: 15px;
    background-color: white !important; /* Change to white */
    border: 1px solid rgba(0, 0, 0, 0.125) !important; /* Keep !important as it was in original */
    border-radius: 5px;
    font-size: 14px !important; /* Keep !important as it was in original */
    font-weight: 300 !important; /* Keep !important as it was in original */
    color: #666 !important; /* Keep !important as it was in original */
    line-height: 1.5;
}

.auteurs-screens-section .quiz-results .summary {
    font-size: 14px !important; /* Keep !important as it was in original */
    font-weight: 300 !important; /* Keep !important as it was in original */
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.auteurs-screens-section .quiz-results .question {
    margin-bottom: 15px;
    padding: 10px;
    border-left: 3px solid #eee;
}

.auteurs-screens-section .quiz-results .correct {
    color: #28a745;
    border-left-color: #28a745;
}

.auteurs-screens-section .quiz-results .incorrect {
    color: #dc3545;
    border-left-color: #dc3545;
}

.auteurs-screens-section .quiz-results .answer {
    margin-left: 20px;
    font-style: italic;
    margin-top: 5px;
}

.auteurs-screens-section .quiz-results .check-icon {
    margin-right: 5px;
    font-weight: bold;
}

.auteurs-screens-section .quiz-results .answer.correct {
    color: #28a745;
    font-weight: 500;
}

.auteurs-screens-section .pre-quiz-section {
    width: 100%;
    max-width: 1000px; /* Keep as it was in original */
    margin: 0 auto;
    padding-top: 5rem;
    position: relative;
}

.auteurs-screens-section .pre-quiz-section .container {
    padding: 20px; /* Keep as it was in original */
    border-radius: 5px; /* Keep as it was in original */
    background-color: white !important; /* Change to white */
    border: 1px solid rgba(0, 0, 0, 0.125) !important; /* Keep !important as it was in original */
    margin-bottom: 1rem; /* Add bottom margin */
    box-shadow: none !important; /* Ensure no box-shadow */
}

.auteurs-screens-section .pre-quiz-section .review-question {
    font-weight: 300;
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9rem;
}

.auteurs-screens-section .pre-quiz-section .review-text {
    font-size: 0.9rem;
    margin-bottom: 15px;
    color: #666;
    font-style: italic;
}

.auteurs-screens-section .pre-quiz-section .review-radio-group {
    margin-bottom: 20px;
}

.auteurs-screens-section .pre-quiz-section .form-group {
    margin-bottom: 5px;
}

.auteurs-screens-section .pre-quiz-section label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.auteurs-screens-section .pre-quiz-section input[type="radio"] {
    margin-right: 10px;
    cursor: pointer;
}

.auteurs-screens-section .pre-quiz-section .review-answer {
    font-size: 1rem;
}

.auteurs-screens-section .pre-quiz-section .btn-text-link {
    color: var(--accent-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid var(--accent-color);
    border-radius: 3px;
    margin-top: 1rem;
    font-size: 1rem;
    background-color: transparent;
    transition: all 0.2s ease;
}

.auteurs-screens-section .pre-quiz-section .btn-text-link:hover {
    background-color: var(--accent-color);
    color: white;
}

.auteurs-screens-section .pre-quiz-section .quiz-card {
    background-color: white !important; /* Change to white */
    border: 1px solid rgba(0, 0, 0, 0.125) !important; /* Keep !important as it was in original */
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 1rem;
}

.auteurs-screens-section .vault-section {
    width: 100%;
    max-width: 1000px; /* Keep as it was in original */
    margin: 0 auto;
    padding-top: 5rem;
    position: relative;
}

.auteurs-screens-section .vault-container {
    background-color: transparent !important; /* Keep !important as it was in original */
    margin-top: 1rem;
}

.auteurs-screens-section .reader-hero-section {
    background-color: transparent !important; /* Keep !important as it was in original */
    padding: 0 !important; /* Keep !important as it was in original */
    margin-bottom: 1rem;
}

.auteurs-screens-section .reader-hero-image-container {
    margin: 0 auto !important; /* Keep !important as it was in original */
}

.auteurs-screens-section .reader-image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 300;
}

.auteurs-screens-section .reader-literary-quote {
    font-size: 0.9rem;
    margin-top: 1rem !important; /* Keep !important as it was in original */
    margin-bottom: 1rem !important; /* Keep !important as it was in original */
}

.auteurs-screens-section .reader-padding-medium .container {
    padding: 15px; /* Keep as it was in original */
    border-radius: 5px; /* Keep as it was in original */
    background-color: #fff !important; /* Keep !important as it was in original */
    margin-bottom: 1rem; /* Add bottom margin */
    box-shadow: none !important; /* Ensure no box-shadow */
}

.auteurs-screens-section .reader-book .card {
    border: none !important; /* Keep !important as it was in original */
    box-shadow: none !important; /* Keep !important as it was in original */
}

.auteurs-screens-section .card-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.auteurs-screens-section .book-summary {
    font-style: italic;
    margin-bottom: 1rem;
}

.auteurs-screens-section .reader-card-middle-section p {
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.auteurs-screens-section .alert-info {
    padding: 0.5rem !important; /* Keep !important as it was in original */
    margin: 0.5rem 0 !important; /* Keep !important as it was in original */
    font-size: 0.8rem !important; /* Keep !important as it was in original */
}

.auteurs-screens-section .reader-card-bottom-section {
    margin-top: 1rem;
}

.auteurs-screens-section .btn-text-link {
    color: var(--accent-color);
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--accent-color);
    border-radius: 3px;
    margin-right: 0.5rem;
    font-size: 0.9rem;
    background-color: transparent;
    transition: all 0.2s ease;
}

.auteurs-screens-section .btn-text-link:hover {
    background-color: var(--accent-color);
    color: white;
}

/* Optional: Add animation for the background content */
.auteurs-screens-section .background-html {
    animation: fadeIn 0.5s ease-in-out;
}

/* Card styles for quiz sections */
.quiz-card,
.quiz-results-card {
    width: 100%;
}

/* Maintain image aspect ratios */
.auteurs-screens-section .img-fluid {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* Adjust spacing for better visual balance */
.auteurs-screens-section .card-body {
    padding: calc(1.25rem * 0.8);  /* Scale down padding */
}

/* Override the line color for section titles in parallax sections */
.auteurs-screens-section h3.section-title::before,
.auteurs-screens-section h3.section-title::after,
.auteurs-screens-section h3.activity-heading::before,
.auteurs-screens-section h3.activity-heading::after,
.auteurs-screens-section .section-title::before,
.auteurs-screens-section .section-title::after,
.auteurs-screens-section .activity-heading::before,
.auteurs-screens-section .activity-heading::after {
    background-color: white !important;
}

/* Section Spacing */
.padding-large {
    padding-top: 2rem;
    padding-bottom: 0rem;
}

section {
    margin-bottom: 2rem;
}

section:last-child {
    margin-bottom: 0;
}

/* Container Override */
.container {
    max-width: 1000px !important; /* Keep !important as it was in original */
    margin: 0 auto;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

/* Footer Section */
.footer-section {
    padding: 2rem 0;
    background-color: #fff;
    border-top: 1px solid #eee;
    margin-top: 2rem;
}

.footer-links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin-top: 10px;
}

.footer-links a {
    color: black;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 300;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.copyright {
    color: #999;
    font-size: 0.6rem;
    margin-top: 15px;
}

/* Responsive Design */
/* Consolidate media queries */
@media (max-width: 768px) {
    /* navbar-brand logo sizing removed - now handled in logo-styles.css */

    .hero-content h2 {
        font-size: 2rem;
    }

    .work-box,
    .feature-box {
        margin-bottom: 2rem;
    }

    .pricing-box {
        margin-bottom: 2rem;
    }
}

@media (max-width: 767.98px) {
    /* Mobile styles for text alignment */
    #what .col-md-5 {
        margin-bottom: 2rem;
    }

    #what .col-md-5:last-child {
         margin-bottom: 0; /* Remove margin from the very last text block */
    }

    #what .col-md-5 section {
        text-align: center;
    }

    #what .col-md-5 section h2,
    #what .col-md-5 section h3,
    #what .col-md-5 section p,
    #what .col-md-5 section ul {
        text-align: center;
    }

    #what .col-md-5 section ul {
        list-style-position: inside;
        padding-left: 0;
    }

    #what .col-md-5 section li {
        text-align: center;
        padding-left: 0;
    }

    /* Ensure headings are centered */
    .heading,
    .highlighted-title {
        text-align: center;
    }

    /* Center title containers */
    .title-container {
        text-align: center;
    }
}

/* Add smooth scrolling to body for parallax effect */
html {
    scroll-behavior: smooth;
}

 


/* ======================
   RESPONSIVE FONT SIZING
   ====================== */

/* Extra small devices (phones less than 576px) */
@media only screen and (max-width: 575px) {
    html {
        font-size: 18px; /* Largest font size for smallest screens */
    }
     /* Adjust navigation font size */
     .nav-link, .nav-radio-group span {
        font-size: 1.1rem !important;
    }
}

/* Small devices (large phones, 576px and up) */
@media only screen and (min-width: 576px) and (max-width: 767px) {
    html {
        font-size: 17px; /* Slightly smaller for larger phones */
    }

 /* Adjust navigation font size */
 .nav-link, .nav-radio-group span {
    font-size: 1.1rem !important;
}

}

/* Medium devices (tablets, 768px and up) */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    html {
        font-size: 16px; /* Transitional size for tablets */
    }

}

/* Large devices (desktops, 992px and up) */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
    html {
        font-size: 16px; /* Standard size for desktops */
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
    html {
        font-size: 16px; /* Maintain standard size for large screens */
    }
}

/* High-DPI mobile devices (for Retina displays and similar) */
@media only screen and (max-width: 767px) and (min-resolution: 2dppx) {
    html {
        font-size: 19px; /* Slightly adjusted for high-resolution small screens */
    }
}

/* Orientation-specific adjustments */
@media only screen and (max-width: 767px) and (orientation: landscape) {
    html {
        font-size: 17px; /* Slightly smaller for landscape mobile */
    }
}
