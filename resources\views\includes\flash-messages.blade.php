{{-- Check for user-specific flash messages (e.g., when admin approves user's first review) --}}
@auth
    @php
        $user = Auth::user();
        $userFlashKey = 'user_flash_message_' . $user->id;
        $userFlashMessage = Session::get($userFlashKey);
        
        // Check for congratulations activity notification
        $congratsActivity = \App\Models\Activity::where('userId', $user->id)
            ->where('reason', 'like', 'Congratulations! Your first review got%')
            ->where('read', 0)
            ->first();
        
        $showCongratulations = $congratsActivity ? true : false;
    @endphp
    
    {{-- Display session-based flash message --}}
    @if ($userFlashMessage)
        <div class="alert alert-{{ $userFlashMessage['type'] ?? 'success' }}">
            {!! $userFlashMessage['message'] !!}
        </div>
        @php
            // Remove the message after displaying it
            Session::forget($userFlashKey);
            if (config('app.debug')) {
                \Log::info("Session flash message displayed and removed for user " . $user->id);
            }
        @endphp
    @endif
    
    {{-- Display congratulations message from Activity --}}
    @if ($showCongratulations)
        <div class="alert alert-success">
            Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)
        </div>
        @php
            // Mark the activity as read after displaying the message
            $congratsActivity->read = 1;
            $congratsActivity->save();
        @endphp
    @endif
@endauth

@if ($message = Session::get('success'))
    {{-- Exclude quiz results messages as they are handled specifically in the review page --}}
    @if (strpos($message, 'quiz-results') === false)
        <div class="alert alert-success">
            {!! $message !!}
        </div>
    @endif
@endif

@if ($message = Session::get('core_auteur_message'))
    <div class="alert alert-info" style="background-color: #e0f7fa; border-color: #b2ebf2; color: #007bff;">
        {!! $message !!}
    </div>
@endif

@if ($message = Session::get('welcome_message'))
    <div class="alert alert-info">
        {!! $message !!}
    </div>
@endif

@if ($message = Session::get('error'))
    <div class="alert alert-danger">
        {!! $message !!}
    </div>
@endif

@if ($message = Session::get('info'))
    <div class="alert alert-info">
        {!! $message !!}
    </div>
@endif

@if ($message = Session::get('warning'))
    <div class="alert alert-warning">
        {!! $message !!}
    </div>
@endif
