<?php

namespace App\Http\Controllers;
use Carbon\Carbon;


use App\Models\Activity;
use App\Models\Advertising;
use App\Models\Book;
use App\Models\Category;
use App\Models\Reader;
use App\Models\Review;
use App\Models\SystemControl;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VaultController extends Controller
{
    public function returnVaultPage()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // User is not authenticated, redirect to login
            return redirect()->route('login');
        }

        $user = Auth::user()->id;
        $vaultCond = Book::get()->where('publish_by', $user)->where('approval_status', '=', 1);
        $categories = Category::all();

        $books = Book::where('publish_by', '!=', $user)->get();
        $readCond = Reader::get()->where('userId', $user)->where('status', '=', 1);
        $allBooks = Advertising::with(['book', 'book.categories', 'book.reviews'])->where('reeader_required', '>', '0')
            ->where('uid', '!=', $user)->get();

        // Log KU books and their points for debugging
        $kuBooks = $allBooks->where('bookPrice', 'KU');
        foreach ($kuBooks as $kuBook) {
            \Log::info('KU Book in Vault', [
                'id' => $kuBook->id,
                'bookId' => $kuBook->bookId,
                'book_title' => $kuBook->book ? $kuBook->book->title : 'Unknown',
                'points' => $kuBook->points,
            ]);
        }

        // return response()->json($allBooks);
        $activeAssignmentsCount = SystemControl::where('key', 'Assignments')->value('value');
        $systemPoints = SystemControl::where('key_type', 'commision')->value('value');
        $KUPoints = SystemControl::where('key_type', 'KU')->value('value');

        $userId = Auth::id();
        $weeklyLimit = (int) SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
        $weeklyCount = Reader::where('userId', $userId)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        if ($weeklyCount >= $weeklyLimit) {
            $allBooks = collect(); // Empty collection
            $vaultMessage = "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review";
            return view('dashboard.vault', compact('books', 'vaultCond', 'categories', 'readCond', 'allBooks', 'activeAssignmentsCount', 'systemPoints', 'KUPoints', 'vaultMessage'));
        }

        // Get current user
        $currentUser = Auth::user();

        // Refresh user from database to ensure we have the latest values
        $currentUser->refresh();

        // Check Reviews on Hold logic
        $reviewsOnHold = SystemControl::where('key_type', 'reviews_on_hold')->first();
        $isReviewsOnHoldEnabled = $reviewsOnHold && $reviewsOnHold->value == 1;

        // Always check if user is waiting for first review approval, regardless of system status
        // This ensures users who were marked as waiting remain restricted until they get approved
        $isWaitingForReview = $currentUser->waiting_for_first_review_approval === true;

        \Log::info("Vault access check for user {$currentUser->id}", [
            'waiting_for_first_review_approval' => $isWaitingForReview,
            'user_id' => $currentUser->id,
            'email' => $currentUser->email,
            'reviews_on_hold_enabled' => $isReviewsOnHoldEnabled
        ]);

        // Apply Reviews on Hold restrictions if user is waiting OR if system is enabled
        if ($isWaitingForReview) {

            // Check if user has active assignments and reset flag if needed
            $userActiveAssignmentsCount = Reader::where('userId', $currentUser->id)->where('status', 1)->count();

            // Only reset the waiting flag if user has no active assignments AND has an approved review
            if ($userActiveAssignmentsCount == 0) {
                // Check if user has any approved reviews (including partial validation)
                $hasApprovedReview = Review::where('userid', $currentUser->id)
                    ->whereIn('reviewStatus', [1, 2])
                    ->exists();

                // Only reset the flag if they have an approved review
                if ($hasApprovedReview) {
                    $currentUser->waiting_for_first_review_approval = false;
                    $currentUser->save();
                    $isWaitingForReview = false; // Update the local variable

                    \Log::info("Reset waiting_for_first_review_approval flag for user {$currentUser->id} - has approved review and no active assignments");
                }
            }

            // If user is still waiting and has no approved reviews, restrict Vault access
            if ($isWaitingForReview) {
                // Check if user has any approved reviews (including partial validation)
                $hasApprovedReview = Review::where('userid', $currentUser->id)
                    ->whereIn('reviewStatus', [1, 2])
                    ->exists();

                // If user is waiting and has no approved reviews, restrict Vault access
                if (!$hasApprovedReview) {
                    $allBooks = collect(); // Empty collection
                    $vaultMessage = 'Dear Auteur, please wait until your first review is approved before being able to take more books for review. See details in our <a href="https://auteurs-space.gitbook.io/auteurs.space/section-3-managing-books-and-reviews/article-3.5-reviews-on-hold-mode-for-first-time-authors" target="_blank" rel="noopener noreferrer" class="knowledge-base-link">Knowledge Base article</a>.';
                    return view('dashboard.vault', compact('books', 'vaultCond', 'categories', 'readCond', 'allBooks', 'activeAssignmentsCount', 'systemPoints', 'KUPoints', 'vaultMessage'));
                }
            }
        }

        return view('dashboard.vault', compact('books', 'vaultCond', 'categories', 'readCond', 'allBooks', 'activeAssignmentsCount', 'systemPoints', 'KUPoints'));
    }



    public function filterBooks(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // User is not authenticated, return JSON error
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        $userId = Auth::id();
        $currentUser = Auth::user();

        // Check Reviews on Hold restrictions
        $isWaitingForReview = $currentUser->waiting_for_first_review_approval === true;

        if ($isWaitingForReview) {
            // Check if user has any approved reviews (including partial validation)
            $hasApprovedReview = Review::where('userid', $currentUser->id)
                ->whereIn('reviewStatus', [1, 2])
                ->exists();

            // If user is waiting and has no approved reviews, return empty result
            if (!$hasApprovedReview) {
                return response()->json([
                    'Message' => 'Dear Auteur, please wait until your first review is approved before being able to take more books for review.'
                ]);
            }
        }

        // Check weekly limit
        $weeklyLimit = (int) SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
        $weeklyCount = Reader::where('userId', $userId)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        if ($weeklyCount >= $weeklyLimit) {
            return response()->json([
                'Message' => "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review"
            ]);
        }

        $query = Advertising::with(['book', 'book.reviews', 'book.categories']);

        $typeFilters = $request->input('type_filter', []);
        $priceRangeFilters = $request->input('price_range_filter', []);
        $categoryFilters = $request->input('category_filter', []);

        // Filter out books by the current user
        $query->whereHas('book', function ($query) use ($userId) {
            $query->where('publish_by', '!=', $userId);
        });

        // Apply type filters
        $query->where(function ($query) use ($typeFilters) {
            if (in_array('free', $typeFilters)) {
                $query->where(function ($query) {
                    $query->where('bookPrice', 0)->whereNot('bookPrice', 'KU');
                });
            }
            if (in_array('Kindle_Unlimited', $typeFilters)) {
                $query->orWhere('bookPrice', 'KU');
            }
            if (in_array('verified', $typeFilters)) {
                $query->orWhere(function($query) {
                    $query->where('require_purchase', 1)
                          ->whereNot('bookPrice', 'KU'); // Exclude KU books from verified purchase filter
                });
            }
        });

        // Apply category filters
        if (!empty($categoryFilters)) {
            $query->whereHas('book.categories', function ($query) use ($categoryFilters) {
                $query->whereIn('id', $categoryFilters);
            });
        }

        // Apply price range filters
        if (!empty($priceRangeFilters)) {
            $query->whereHas('book', function ($query) use ($priceRangeFilters) {
                $query->whereIn('bookPrice', $priceRangeFilters);
            });
        }

        // Ensure reader_required condition
        $query->where('reeader_required', '>', 0);

        // Filter out books already reviewed by the current user
        $query->whereDoesntHave('book.reviews', function ($query) use ($userId) {
            $query->where('userid', $userId);
        });

        // Filter out books already read by the current user
        $query->whereDoesntHave('book.reader', function ($query) use ($userId) {
            $query->where('userid', $userId)->where('status', 1);
        });

        // Sort by most tokens if selected
        if (in_array('most_tokens', $typeFilters)) {
            $query->orderByRaw('CAST(points AS UNSIGNED) DESC');
        }

        $books = $query->get();

        if ($books->isEmpty()) {
            return response()->json(['Message' => 'No Books Found Matching Results']);
        }

        $systemPoints = SystemControl::where('key_type', 'commision')->value('value');
        $KUPoints = SystemControl::where('Key_type', 'KU')->value('value');

        return response()->json(['books' => $books, 'systemPoints' => $systemPoints, 'KUPoints' => $KUPoints]);
    }





    public function testFunction()
    {

        return response()->json('Hello World!');
    }




    public function catFilter(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // User is not authenticated, redirect to login
            return redirect()->route('login');
        }

        $user = Auth::user()->id;

        $advertisings = Advertising::select('advertisings.*', 'books.*', 'categories.category as category_name')
            ->join('books', 'advertisings.bookId', '=', 'books.id')
            ->join('categories', 'books.category', '=', 'categories.id')
            ->where('publish_by', '!=', $user)
            ->where('category.id', '=', $request->cat)
            ->get();

        $cat = Category::get();
        $vaultCond = Book::get()->where('publish_by', $user)->where('approval_status', '=', 1);

        // You can return data as JSON
        return response()->json([
            'advertisings' => $advertisings,
            'cat' => $cat,
            'vaultCond' => $vaultCond,
        ]);
    }





    public function getSingleBook($amount, $slug)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // User is not authenticated, redirect to login
            session()->put('url.intended', url()->current());
            return redirect()->route('login');
        }

        $userId = Auth::id();
        $currentUser = Auth::user();

        // Debug log
        \Log::info('getSingleBook called', ['amount' => $amount, 'slug' => $slug]);

        // Check Reviews on Hold restrictions
        $isWaitingForReview = $currentUser->waiting_for_first_review_approval === true;

        if ($isWaitingForReview) {
            // Check if user has any approved reviews (including partial validation)
            $hasApprovedReview = Review::where('userid', $currentUser->id)
                ->whereIn('reviewStatus', [1, 2])
                ->exists();

            // If user is waiting and has no approved reviews, redirect back to vault
            if (!$hasApprovedReview) {
                return redirect()->route('vault')->withErrors(['error' => 'Dear Auteur, please wait until your first review is approved before being able to take more books for review.']);
            }
        }

        // Check weekly limit
        $weeklyLimit = (int) SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
        $weeklyCount = Reader::where('userId', $userId)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        if ($weeklyCount >= $weeklyLimit) {
            return redirect()->route('vault')->withErrors(['error' => "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review"]);
        }

        // Load book with categories relationship
        $book = Book::with(['categories', 'reviews'])
            ->where('slug', $slug)
            ->first();

        if (!$book) {
            return redirect()->route('vault')->withErrors(['error' => 'Book not found']);
        }

        \Log::info('Book found', ['id' => $book->id, 'title' => $book->title]);

        if ($amount != 'view') {
            $isBookReviewedByUser = Review::where('bookId', $book->id)
                ->where('userid', Auth::user()->id)
                ->first();

            if ($isBookReviewedByUser) {
                return redirect(route('vault.singleBook', ['amount' => 'view', 'slug' => $slug]));
            }
        }

        // SIMPLIFIED APPROACH: First, find ALL advertising records for this book
        $allAdvertising = Advertising::where('bookId', $book->id)->get();
        \Log::info('All advertising records', ['count' => $allAdvertising->count(), 'records' => $allAdvertising->toArray()]);

        // Find the KU advertising record if it exists
        $kuAdvertising = $allAdvertising->where('bookPrice', 'KU')->first();

        // Find the appropriate advertising record based on the amount
        if ($amount == 'kdp') {
            $advertising = $kuAdvertising ?? $allAdvertising->first();
            $bookPrice = 'KU';

            // Special handling for KU books to ensure consistency with Vault display
            if ($advertising && $advertising->bookPrice == 'KU') {
                $Points = $advertising->points;
                \Log::info('Using KU book', ['advertising_id' => $advertising->id, 'points' => $Points]);
            } else {
                $Points = $advertising ? $advertising->points : 0;
                \Log::info('Using non-KU book as fallback', ['advertising_id' => $advertising ? $advertising->id : null, 'points' => $Points]);
            }
        } elseif ($amount == 'paid') {
            $advertising = $allAdvertising->where('bookPrice', '>', 0)->where('bookPrice', '!=', 'KU')->first() ?? $allAdvertising->first();
            $bookPrice = $advertising ? $advertising->bookPrice : 0;
            $Points = $advertising ? $advertising->points : 0;
        } elseif ($amount == 'view') {
            $review = Reader::where('bookId', $book->id)
                ->where('userId', Auth::user()->id)
                ->first();

            if ($review) {
                if ($review->type == 'KDP') {
                    $advertising = $kuAdvertising ?? $allAdvertising->first();
                    $bookPrice = 'KU';
                    $Points = $advertising ? $advertising->points : $review->rpoints;
                } else {
                    if ($review->type == 'Paid') {
                        $advertising = $allAdvertising->where('bookPrice', '>', 0)->where('bookPrice', '!=', 'KU')->first() ?? $allAdvertising->first();
                    } else {
                        $advertising = $allAdvertising->where('bookPrice', 0)->first() ?? $allAdvertising->first();
                    }
                    $bookPrice = $advertising ? $advertising->bookPrice : 0;
                    $Points = $review->rpoints;
                }
            } else {
                $advertising = $allAdvertising->first();
                $bookPrice = $advertising ? $advertising->bookPrice : 0;
                $Points = $advertising ? $advertising->points : 0;
            }
        } else { // free
            // Ensure we get the free advertising record
            $advertising = $allAdvertising->where('bookPrice', 0)->where('bookPrice', '!=', 'KU')->first();

            // If no free record found, try to get any non-KU record as fallback
            if (!$advertising) {
                $advertising = $allAdvertising->where('bookPrice', '!=', 'KU')->first();
            }

            // Last resort: get any record
            if (!$advertising) {
                $advertising = $allAdvertising->first();
            }

            // Ensure bookPrice is set to 0 for free books
            $bookPrice = 0;
            $Points = $advertising ? $advertising->points : 0;

            \Log::info('Using free book', [
                'advertising_id' => $advertising ? $advertising->id : null,
                'points' => $Points,
                'bookPrice' => $bookPrice
            ]);
        }

        // Log the final values
        \Log::info('Final values', [
            'bookPrice' => $bookPrice,
            'Points' => $Points,
            'advertising_id' => $advertising ? $advertising->id : null,
            'advertising_points' => $advertising ? $advertising->points : null,
            'advertising_bookPrice' => $advertising ? $advertising->bookPrice : null
        ]);

        $user = Auth::user()->id;
        $reader = Reader::where('bookId', $book->id)
            ->where('userId', '=', $user)
            ->where('status', '=', 1)
            ->first();

        $KUPoints = SystemControl::where('key_type', 'KU')->value('value');
        $systemPoints = SystemControl::where('key_type', 'commision')->value('value');

        return view('vault.vault-single-book', compact(
            'book',
            'reader',
            'amount',
            'Points',
            'KUPoints',
            'bookPrice',
            'systemPoints',
            'advertising'
        ));
    }
}
