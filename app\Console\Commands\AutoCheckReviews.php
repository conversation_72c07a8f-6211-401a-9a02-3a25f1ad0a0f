<?php

namespace App\Console\Commands;

use App\Models\Review;
use App\Models\SystemControl;
use App\Services\AmazonReviewVerificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Models\Book;
use App\Models\Wallet;
use App\Models\Advertising;
use App\Models\Activity;
use App\Models\User;

class AutoCheckReviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:auto-check-reviews';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically check pending reviews based on configured timeframes';

    /**
     * The Amazon review verification service
     *
     * @var AmazonReviewVerificationService
     */
    protected $verificationService;

    /**
     * Create a new command instance.
     *
     * @param AmazonReviewVerificationService $verificationService
     * @return void
     */
    public function __construct(AmazonReviewVerificationService $verificationService)
    {
        parent::__construct();
        $this->verificationService = $verificationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting auto check reviews process...');
        Log::info('Starting auto check for pending reviews... Server time: ' . date('Y-m-d H:i:s') . ' (' . date_default_timezone_get() . ')');

        // Display diagnostic information for debugging
        $this->displayDiagnosticInfo();

        // Get system settings
        $startDay = SystemControl::where('key_type', 'day_autocheck_starts')->first();
        $endDay = SystemControl::where('key_type', 'day_autocheck_ends')->first();
        $lastRunSetting = SystemControl::where('key_type', 'last_autocheck_run')->first();
        $autocheck_rate = SystemControl::where('key_type', 'autocheck_rate')->first();
        $manualVerificationMode = SystemControl::where('key_type', 'autoManualApprove')->first();

        // Log the manual verification mode setting for debugging
        $this->info("Manual Verification Mode: " . ($manualVerificationMode ? $manualVerificationMode->value : 'not set'));
        Log::info("Manual Verification Mode: " . ($manualVerificationMode ? $manualVerificationMode->value : 'not set') .
                 " (0 = Manual Verification, 1 = Auto Verification)");

        $now = time();

        if ($startDay === null || $endDay === null || $startDay->value === '' || $endDay->value === '') {
            $this->info('Auto check is disabled. No start or end day configured.');
            Log::info('Auto check is disabled. No start or end day configured.');
            return 0;
        }

        // IMPORTANT: Update the last run time BEFORE running the checks
        // This prevents multiple runs if the command takes a long time to execute
        if ($lastRunSetting) {
            $lastRunSetting->value = $now;
            $lastRunSetting->save();
            Log::info("Updated last_autocheck_run to: $now (" . date('Y-m-d H:i:s', $now) . ")");
        } else {
            // Create setting if it doesn't exist
            $newSetting = new SystemControl();
            $newSetting->key = 'Last Autocheck Run';
            $newSetting->key_type = 'last_autocheck_run';
            $newSetting->value = $now;
            $newSetting->save();
            Log::info("Created new last_autocheck_run with value: $now (" . date('Y-m-d H:i:s', $now) . ")");
        }

        $startDayValue = (int)$startDay->value;
        $endDayValue = (int)$endDay->value;

        // Ensure minimum values
        $startDayValue = max(0, $startDayValue); // Minimum 0 hours
        $endDayValue = max(1, $endDayValue);    // Minimum 1 hour

        // IMPORTANT: Both startDay and endDay are now in HOURS
        $this->info("Auto check configuration: Start hours {$startDayValue}, End hours {$endDayValue}");
        Log::info("Auto check configuration: Start hours {$startDayValue}, End hours {$endDayValue}");

        // Convert to Carbon instances for easier date comparison
        $now = Carbon::now();
        $autocheckRateHours = $autocheck_rate ? (int)$autocheck_rate->value : 24;

        // Handle immediate check if startDay = 0 (check reviews immediately after submission)
        if ($startDayValue === 0) {
            $this->info("Immediate checking enabled (startDay = 0)");
            $this->checkImmediateReviews();
        }

        // Process ALL pending reviews regardless of timeframe to ensure none are missed
        $allPendingReviews = Review::where('reviewStatus', 0)->get();

        if ($allPendingReviews->isEmpty()) {
            $this->info("No pending reviews found to process.");
            Log::info("No pending reviews found to process.");
            return 0;
        }

        $this->info("Found {$allPendingReviews->count()} total pending reviews");
        Log::info("Total pending reviews found: " . $allPendingReviews->count());

        // Process all pending reviews
        $approvedCount = 0;
        $partiallyApprovedCount = 0;
        $rejectedCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $manualVerificationRequiredCount = 0;

        foreach ($allPendingReviews as $review) {
            // Check if this review should be rechecked based on last_checked_at and autocheck_rate
            $shouldCheck = false;
            $reviewCreatedAt = Carbon::parse($review->created_at);
            $hoursOld = $reviewCreatedAt->diffInHours($now);

            // Log review details for debugging
            $this->info("Review #{$review->id} - Created: {$review->created_at}, Hours old: {$hoursOld}");
            Log::info("Review #{$review->id} - Created: {$review->created_at}, Hours old: {$hoursOld}");

            if ($review->last_checked_at === null) {
                // Never checked before - should check if it's past the startDay hours threshold
                $shouldCheck = $hoursOld >= $startDayValue;

                if (!$shouldCheck) {
                    $this->info("Skipping review #{$review->id} - Only {$hoursOld} hours old (need {$startDayValue})");
                    Log::info("Skipping review #{$review->id} - Only {$hoursOld} hours old (need {$startDayValue})");
                    $skippedCount++;
                    continue;
                }

                $this->info("Review #{$review->id} - Never checked before and {$hoursOld} hours old - WILL CHECK");
                Log::info("Review #{$review->id} - Never checked before and {$hoursOld} hours old - WILL CHECK");
            } else {
                // Previously checked - should recheck if last check was more than autocheck_rate hours ago
                $reviewLastCheckedAt = Carbon::parse($review->last_checked_at);
                $hoursSinceLastCheck = $reviewLastCheckedAt->diffInHours($now);
                $shouldCheck = $hoursSinceLastCheck >= $autocheckRateHours;

                $this->info("Review #{$review->id} - Last checked: {$review->last_checked_at}, Hours since check: {$hoursSinceLastCheck}, Rate: {$autocheckRateHours}");
                Log::info("Review #{$review->id} - Last checked: {$review->last_checked_at}, Hours since check: {$hoursSinceLastCheck}, Rate: {$autocheckRateHours}");

                if (!$shouldCheck) {
                    $this->info("Skipping review #{$review->id} - Checked {$hoursSinceLastCheck} hours ago (rate: {$autocheckRateHours})");
                    Log::info("Skipping review #{$review->id} - Checked {$hoursSinceLastCheck} hours ago (rate: {$autocheckRateHours})");
                    $skippedCount++;
                    continue;
                }

                $this->info("Review #{$review->id} - Last checked {$hoursSinceLastCheck} hours ago - WILL CHECK");
                Log::info("Review #{$review->id} - Last checked {$hoursSinceLastCheck} hours ago - WILL CHECK");
            }

            try {
                // Always update last_checked_at before checking
                $oldLastCheckedAt = $review->last_checked_at;
                $review->last_checked_at = now();
                $review->save();
                Log::info("Updated last_checked_at for review #{$review->id} from: " .
                    ($oldLastCheckedAt ?: 'never') . " to: " . $review->last_checked_at);

                // Check if review exists on Amazon
                $result = $this->verificationService->checkReviewExists($review);
                Log::info("Review #{$review->id} check result: " . json_encode($result));

                if ($result['success']) {
                    // Review found - handle based on type
                    if ($review->type == 'Paid' && !$result['verified_purchase']) {
                        // Partially approve VP review without VP verification
                        $this->info("Review #{$review->id} found but not VP - PARTIALLY APPROVING");
                        Log::info("Review #{$review->id} found but not VP - PARTIALLY APPROVING");
                        $this->partiallyApproveReview($review);
                        $partiallyApprovedCount++;
                    } else {
                        // Fully approve the review
                        $this->info("Review #{$review->id} found and valid - FULLY APPROVING");
                        Log::info("Review #{$review->id} found and valid - FULLY APPROVING");
                        $this->fullyApproveReview($review);
                        $approvedCount++;
                    }
                } else {
                    // Review not found - check if we're past the end time
                    if ($hoursOld >= $endDayValue) {
                        // We're past the end time - handle based on verification mode
                        // If autoManualApprove value is 0, it means "Manual Verification" mode is selected
                        // If autoManualApprove value is 1, it means "Auto Verification" mode is selected

                        // Make a new query to ensure we get the latest value
                        $freshManualVerificationMode = SystemControl::where('key_type', 'autoManualApprove')->first();
                        $manualVerificationValue = $freshManualVerificationMode ? $freshManualVerificationMode->value : null;

                        $this->info("Review #{$review->id} final verification check - Manual Mode: " .
                            ($manualVerificationValue === 0 ? 'MANUAL' :
                            ($manualVerificationValue === 1 ? 'AUTO' : 'UNKNOWN (' . $manualVerificationValue . ')')));

                        Log::info("Review #{$review->id} final verification check - Manual Mode: " .
                            ($manualVerificationValue === 0 ? 'MANUAL' :
                            ($manualVerificationValue === 1 ? 'AUTO' : 'UNKNOWN (' . $manualVerificationValue . ')')));

                        if ($manualVerificationValue === 0) {
                            // Manual verification mode - mark for manual review
                            $this->info("Review #{$review->id} past end time and not found - MARKING FOR MANUAL VERIFICATION");
                            Log::info("Review #{$review->id} past end time and not found - MARKING FOR MANUAL VERIFICATION");
                            $review->reviewStatus = 4; // New status for manual rejection verification
                            $review->save();
                            $manualVerificationRequiredCount++;
                        } else {
                            // Auto verification mode - reject the review
                            $this->info("Review #{$review->id} past end time and not found - AUTO REJECTING");
                            Log::info("Review #{$review->id} past end time and not found - AUTO REJECTING");
                            $this->rejectReview($review);
                            $rejectedCount++;
                        }
                    } else {
                        // Not past end time yet - already updated last_checked_at above
                        $this->info("Review #{$review->id} not found but still within timeframe - WAITING");
                        Log::info("Review #{$review->id} not found but still within timeframe - WAITING");
                        $skippedCount++;
                    }
                }
            } catch (\Exception $e) {
                $this->error("Error processing review #{$review->id}: " . $e->getMessage());
                Log::error("Error processing review #{$review->id}: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                $errorCount++;
            }
        }

        $this->info("Auto check completed. Results:");
        $this->info("- Approved: {$approvedCount}");
        $this->info("- Partially Approved: {$partiallyApprovedCount}");
        $this->info("- Rejected: {$rejectedCount}");
        $this->info("- Manual Verification Required: {$manualVerificationRequiredCount}");
        $this->info("- Skipped: {$skippedCount}");
        $this->info("- Errors: {$errorCount}");

        Log::info("Auto check completed. Results: Approved={$approvedCount}, PartiallyApproved={$partiallyApprovedCount}, " .
                 "Rejected={$rejectedCount}, ManualVerificationRequired={$manualVerificationRequiredCount}, " .
                 "Skipped={$skippedCount}, Errors={$errorCount}");

        return 0;
    }

    /**
     * Display diagnostic information for debugging autocheck issues
     */
    private function displayDiagnosticInfo()
    {
        // Display server timezone information
        $serverTimezone = date_default_timezone_get();
        $serverTime = date('Y-m-d H:i:s');
        $utcTime = gmdate('Y-m-d H:i:s');

        $this->info("Server timezone: {$serverTimezone}");
        $this->info("Server time: {$serverTime}");
        $this->info("UTC time: {$utcTime}");

        // Get and display last autocheck run info
        $lastAutocheckRun = SystemControl::where('key_type', 'last_autocheck_run')->first();
        if ($lastAutocheckRun) {
            $timestamp = (int)$lastAutocheckRun->value;
            $lastRunTime = Carbon::createFromTimestamp($timestamp);
            $minutesAgo = $lastRunTime->diffInMinutes(Carbon::now());
            $this->info("Last autocheck run: {$lastRunTime->format('Y-m-d H:i:s')} ({$minutesAgo} minutes ago)");

            // Check for future timestamps
            if ($lastRunTime->isFuture()) {
                $this->warn("WARNING: Last autocheck run timestamp is in the future! This can cause scheduling issues.");
            }
        } else {
            $this->info("Last autocheck run: never");
        }

        // Get rate and frequency information
        $autocheckRate = SystemControl::where('key_type', 'autocheck_rate')->first();
        $rate = $autocheckRate ? (int)$autocheckRate->value : 24;
        $this->info("Autocheck rate: {$rate} hours");

        // Count pending reviews
        $pendingCount = Review::where('reviewStatus', 0)->count();
        $this->info("Total pending reviews: {$pendingCount}");

        // Count reviews waiting for first check
        $neverCheckedCount = Review::where('reviewStatus', 0)
            ->whereNull('last_checked_at')
            ->count();
        $this->info("Reviews never checked: {$neverCheckedCount}");
    }

    /**
     * Handle immediate reviews (startDay = 0)
     */
    private function checkImmediateReviews()
    {
        // Get the manual verification mode setting
        $manualVerificationMode = SystemControl::where('key_type', 'autoManualApprove')->first();

        // Process reviews that haven't been checked yet
        $immediateReviews = Review::where('reviewStatus', 0)
            ->whereNull('last_checked_at')
            ->get();

        $this->info("Found {$immediateReviews->count()} reviews for immediate checking");

        foreach ($immediateReviews as $review) {
            $this->info("Immediate checking review #{$review->id}...");

            try {
                // Update last_checked_at
                $review->last_checked_at = now();
                $review->save();

                // Just check if review exists, don't auto-approve yet
                $result = $this->verificationService->checkReviewExists($review);

                if ($result['success']) {
                    $this->info("Review #{$review->id} found in immediate check");

                    // Save review link if available
                    if (!empty($result['review_link']) && empty($review->review_link)) {
                        $review->review_link = $result['review_link'];
                        $review->save();
                    }
                } else {
                    $this->info("Review #{$review->id} not found in immediate check");

                    // If this review needs immediate check and wasn't found, we might need to handle it based on the manual verification mode
                    // However, usually we'll wait for the full end time before making a decision
                }
            } catch (\Exception $e) {
                $this->error("Error in immediate check for review #{$review->id}: " . $e->getMessage());
                Log::error("Error in immediate check for review #{$review->id}: " . $e->getMessage());
            }
        }
    }

    /**
     * Get pending reviews that are in the configured check range
     *
     * @param int $startDay
     * @param int $endDay
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getPendingReviewsInRange($startDay, $endDay)
    {
        $now = Carbon::now();

        // Treat endDay as days (maximum age of reviews to check)
        $startDate = $now->copy()->subDays($endDay);

        // Treat startDay as hours (minimum age of reviews to check)
        // A review should be at least startDay hours old before checking
        $endDate = $now->copy()->subHours($startDay);

        // Log the calculated date range
        Log::info("Checking reviews in time range: from {$startDate->format('Y-m-d H:i:s')} to {$endDate->format('Y-m-d H:i:s')}");
        Log::info("This means reviews created between {$endDay} days ago and {$startDay} hours ago");

        // Get autocheck rechecking rate in hours (how often to recheck a review)
        $autocheck_rate_setting = SystemControl::where('key_type', 'autocheck_rate')->first();
        $autocheck_rate = $autocheck_rate_setting ? (int)$autocheck_rate_setting->value : 24; // Default to 24h

        // Make sure autocheck_rate is at least 1 hour
        $autocheck_rate = max(1, $autocheck_rate);

        Log::info("Autocheck rate is set to {$autocheck_rate} hours");

        // Calculate the time for last checking cutoff
        $recheckBefore = $now->copy()->subHours($autocheck_rate);

        Log::info("Reviews last checked before {$recheckBefore->format('Y-m-d H:i:s')} will be rechecked");

        // Get reviews that are:
        // 1. Pending approval (reviewStatus = 0)
        // 2. Within the configured time range (created between startDate and endDate)
        // 3. Either never checked, or last checked more than autocheck_rate hours ago
        $reviews = Review::where('reviewStatus', 0)
            ->where(function($query) use ($startDate, $endDate) {
                // Get reviews created from [now - endDay days] to [now - startDay hours]
                // For example, if endDay=7 days and startDay=1 hour, reviews from 7 days ago to 1 hour ago
                $query->where('created_at', '>=', $startDate)
                      ->where('created_at', '<=', $endDate);
            })
            ->where(function($query) use ($recheckBefore) {
                $query->whereNull('last_checked_at')
                      ->orWhere('last_checked_at', '<=', $recheckBefore);
            })
            ->get();

        // Extra debug logs
        Log::info("Query parameters: Start date: " . $startDate->toDateTimeString() .
                 ", End date: " . $endDate->toDateTimeString() .
                 ", Recheck before: " . $recheckBefore->toDateTimeString());
        Log::info("Found " . $reviews->count() . " pending reviews in date range that need checking");

        // Log each review that will be checked
        foreach ($reviews as $review) {
            $hoursAgo = Carbon::parse($review->created_at)->diffInHours($now);
            $lastCheckedText = $review->last_checked_at ?
                "last checked " . Carbon::parse($review->last_checked_at)->diffInHours($now) . " hours ago" :
                "never checked";

            Log::info("Review #{$review->id} will be checked. Created {$hoursAgo} hours ago, {$lastCheckedText}");
        }

        return $reviews;
    }

    /**
     * Fully approve a review
     *
     * @param \App\Models\Review $review
     * @return void
     */
    private function fullyApproveReview($review)
    {
        try {
            Log::info("Starting full approval for review #{$review->id}");

            // Update status to approved
            $review->reviewStatus = 1; // Validated status
            $review->save();

            // Handle Reviews on Hold logic - unlock features if this is user's first approved review
            $user = User::find($review->userid);
            if ($user && ($user->waiting_for_first_review_approval ?? false)) {
                // Check if this is their first approved review (including partial validation)
                $approvedReviewsCount = Review::where('userid', $review->userid)
                    ->whereIn('reviewStatus', [1, 2])
                    ->count();
                
                if ($approvedReviewsCount == 1) { // This is their first approved review
                    // Unlock the user's features and reset tracking flags
                    $user->waiting_for_first_review_approval = false;
                    $user->has_used_first_feature = false;
                    $user->has_taken_first_vault_book = false;
                    $user->save();
                    
                    // Create a congratulatory activity
                    $congratsActivity = new \App\Models\Activity;
                    $congratsActivity->userid = $review->userid; // Use userid field to match database schema
                    $congratsActivity->bookId = $review->bookId;
                    $congratsActivity->reason = "Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)";
                    $congratsActivity->read = 0; // Mark as unread
                    $congratsActivity->save();
                    
                    Log::info("Reviews on Hold restrictions lifted for user #{$review->userid} after first review approval");
                }
            }

            // Create notification
            try {
                $book = Book::find($review->bookId);
                $bookTitle = $book ? $book->title : 'Unknown book';

                $notificationActivity = new Activity();
                $notificationActivity->userId = $review->userId;
                $notificationActivity->bookId = $review->bookId;
                $notificationActivity->reason = "Your review for '{$bookTitle}' was fully validated.";
                $notificationActivity->save();

                Log::info("Created notification for approved review #{$review->id}");
            } catch (\Exception $e) {
                Log::error("Error creating notification for review #{$review->id}: " . $e->getMessage());
            }

            Log::info("Review #{$review->id} successfully fully approved");
        } catch (\Exception $e) {
            Log::error("Error in fullyApproveReview for review #{$review->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Partially approve a review (for VP reviews without VP verification)
     *
     * @param \App\Models\Review $review
     * @return void
     */
    private function partiallyApproveReview($review)
    {
        try {
            Log::info("Starting partial approval for review #{$review->id}");

            // Update status to partially approved
            $review->reviewStatus = 2; // Partially validated status
            $review->save();

            // Handle Reviews on Hold logic - unlock features if this is user's first approved review
            $user = User::find($review->userid);
            if ($user && ($user->waiting_for_first_review_approval ?? false)) {
                // Check if this is their first approved review (including partial validation)
                $approvedReviewsCount = Review::where('userid', $review->userid)
                    ->whereIn('reviewStatus', [1, 2])
                    ->count();
                
                if ($approvedReviewsCount == 1) { // This is their first approved review
                    // Unlock the user's features and reset tracking flags
                    $user->waiting_for_first_review_approval = false;
                    $user->has_used_first_feature = false;
                    $user->has_taken_first_vault_book = false;
                    $user->save();
                    
                    // Create a congratulatory activity
                    $congratsActivity = new \App\Models\Activity;
                    $congratsActivity->userid = $review->userid; // Use userid field to match database schema
                    $congratsActivity->bookId = $review->bookId;
                    $congratsActivity->reason = "Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)";
                    $congratsActivity->read = 0; // Mark as unread
                    $congratsActivity->save();
                    
                    Log::info("Reviews on Hold restrictions lifted for user #{$review->userid} after first partial review approval");
                }
            }

            // Create notification
            try {
                $book = Book::find($review->bookId);
                $bookTitle = $book ? $book->title : 'Unknown book';

                // Try to get VP bonus points if applicable
                $advertising = null;
                $vpBonusPoints = 0;

                if ($book) {
                    $advertising = \App\Models\Advertising::where('bookId', $book->id)
                        ->where('bookPrice', '>', '0')
                        ->whereNot('bookPrice', 'KU')
                        ->first();

                    if ($advertising) {
                        $vpBonus = \App\Models\SystemControl::where('key_type', 'VP')
                            ->where('key', $advertising->bookPrice)
                            ->first();

                        $vpBonusPoints = $vpBonus ? $vpBonus->value : 0;
                    }
                }

                // Check if similar notifications already exist to prevent duplicates
                $existingReviewerNotification = Activity::where('userid', $review->userid)
                    ->where('bookId', $review->bookId)
                    ->where('reason', 'like', "%{$bookTitle}%Approved Partially%")
                    ->where('created_at', '>=', now()->subMinutes(5))
                    ->first();

                $existingAuthorNotification = null;
                if ($book) {
                    $existingAuthorNotification = Activity::where('userid', $book->publish_by)
                        ->where('bookId', $review->bookId)
                        ->where('reason', 'like', "%{$bookTitle}%not stamped as a Verified Purchase%")
                        ->where('created_at', '>=', now()->subMinutes(5))
                        ->first();
                }

                // Notification for reviewer
                if (!$existingReviewerNotification) {
                    $notificationActivity = new Activity();
                    $notificationActivity->userid = $review->userid;
                    $notificationActivity->notification_type = 'reviewer';
                    $notificationActivity->bookId = $review->bookId;
                    $notificationActivity->reason = "Your review of \"{$bookTitle}\" was Approved Partially. It was not stamped as Verified Purchase on Amazon for some reason. You received an additional {$vpBonusPoints} Rewards for the VP option on this review, so we have to deduct those Rewards from your account.";
                    $notificationActivity->save();
                    Log::info("Created reviewer notification for partial approval of review #{$review->id}");
                } else {
                    Log::info("Skipped creating duplicate reviewer notification for partial approval of review #{$review->id}");
                }

                // Notification for author
                if ($book && !$existingAuthorNotification) {
                    $authorNotification = new Activity();
                    $authorNotification->userid = $book->publish_by;
                    $authorNotification->notification_type = 'author';
                    $authorNotification->bookId = $review->bookId;
                    $authorNotification->reason = "The review of \"{$bookTitle}\" was published and Approved. However, it was not stamped as a Verified Purchase review on Amazon.com. This is most likely due to a mistake by your reviewer. You spent an additional {$vpBonusPoints} Rewards for the VP option on this review, so we have returned those Rewards to your account.";
                    $authorNotification->save();
                    Log::info("Created author notification for partial approval of review #{$review->id}");
                } else if ($book) {
                    Log::info("Skipped creating duplicate author notification for partial approval of review #{$review->id}");
                }

                Log::info("Created notifications for partially approved review #{$review->id}");
            } catch (\Exception $e) {
                Log::error("Error creating notification for review #{$review->id}: " . $e->getMessage());
            }

            Log::info("Review #{$review->id} successfully partially approved");
        } catch (\Exception $e) {
            Log::error("Error in partiallyApproveReview for review #{$review->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reject a review
     *
     * @param \App\Models\Review $review
     * @return void
     */
    private function rejectReview($review)
    {
        try {
            Log::info("Starting rejection for review #{$review->id}");

            // Get book and user information
            $book = Book::find($review->bookId);
            if (!$book) {
                throw new \Exception("Book not found for review #{$review->id}");
            }

            // Update review status to rejected
            $review->reviewStatus = 3; // Rejected status
            $review->save();

            // Get user wallet - handle case sensitivity
            $userId = $review->userId ?? $review->userid ?? null;
            if (!$userId) {
                throw new \Exception("Review has no valid userId field: #{$review->id}");
            }

            $userWallet = Wallet::where('userId', $userId)->first();
            if (!$userWallet) {
                // Try with lowercase field name
                $userWallet = Wallet::where('userid', $userId)->first();
            }

            if (!$userWallet) {
                throw new \Exception("User wallet not found for user ID: {$userId}, review #{$review->id}");
            }

            // Get author wallet - handle case sensitivity
            $authorId = $book->publish_by;
            $authorWallet = Wallet::where('userId', $authorId)->first();
            if (!$authorWallet) {
                // Try with lowercase field name
                $authorWallet = Wallet::where('userid', $authorId)->first();
            }

            if (!$authorWallet) {
                throw new \Exception("Author wallet not found for author ID: {$authorId}, review #{$review->id}");
            }

            // Get advertising points for book
            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->where('bookPrice', '<>', 'KU')
                    ->first();
            } elseif ($review->type == 'KDP') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', 'KU')
                    ->first();
            } else {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '<=', '0')
                    ->where('bookPrice', '<>', 'KU')
                    ->first();
            }

            if (!$advertising) {
                throw new \Exception("Advertising not found for review #{$review->id}");
            }

            // Get points awarded to reader
            $pointsAwarded = $review->quiz_score_points ?? 0;
            Log::info("Reverting {$pointsAwarded} points for rejected review #{$review->id}");

            // Deduct points from reader
            $userWallet->currentBalance = $userWallet->currentBalance - $pointsAwarded;
            $userWallet->save();

            // Return points to author
            $authorWallet->currentBalance = $authorWallet->currentBalance + $pointsAwarded;
            $authorWallet->save();

            Log::info("Review #{$review->id} rejected: {$pointsAwarded} points transferred from user #{$userId} to author #{$book->publish_by}");

            // Create notifications
            $this->createRejectionNotifications($review, $book, $userId, $authorId, $pointsAwarded);

            Log::info("Review #{$review->id} successfully rejected");
        } catch (\Exception $e) {
            Log::error("Error in rejectReview for review #{$review->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create notifications for rejected review
     *
     * @param \App\Models\review $review
     * @param \App\Models\Book $book
     * @param int $userId
     * @param int $authorId
     * @param int $pointsAwarded
     * @return void
     */
    private function createRejectionNotifications($review, $book, $userId, $authorId, $pointsAwarded)
    {
        // Get end day setting
        $endDay = SystemControl::where('key_type', 'day_autocheck_ends')->first();
        $endDayValue = $endDay ? (int)$endDay->value : 240; // Default to 240 hours (10 days)

        // Create notification for reader
        try {
            $readerActivity = new Activity();
            $readerActivity->userId = $userId;
            $readerActivity->bookId = $review->bookId;
            $readerActivity->reason = "Your review for '{$book->title}' was automatically rejected: not found on Amazon within {$endDayValue} hours. {$pointsAwarded} tokens have been returned to the author.";

            if ($readerActivity->save()) {
                Log::info("Created reader notification for rejected review #{$review->id} for user #{$userId}");
            } else {
                Log::error("Failed to save reader notification for review #{$review->id}");
            }
        } catch (\Exception $e) {
            Log::error("Error creating reader notification: " . $e->getMessage());
        }

        // Create notification for author
        try {
            $authorActivity = new Activity();
            $authorActivity->userId = $authorId;
            $authorActivity->bookId = $review->bookId;
            $authorActivity->reason = "A review for your book '{$book->title}' was automatically rejected: not found on Amazon within {$endDayValue} hours. {$pointsAwarded} tokens have been returned to your account.";

            if ($authorActivity->save()) {
                Log::info("Created author notification for rejected review #{$review->id} for author #{$authorId}");
            } else {
                Log::error("Failed to save author notification for review #{$review->id}");
            }
        } catch (\Exception $e) {
            Log::error("Error creating author notification: " . $e->getMessage());
        }
    }
}
