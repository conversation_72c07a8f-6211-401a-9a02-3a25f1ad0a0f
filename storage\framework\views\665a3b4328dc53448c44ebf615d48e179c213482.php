
<?php $__env->startSection('page_title', 'Vault'); ?>
<?php $__env->startSection('page_content'); ?>

<!-- DEBUG: Check if vaultMessage is set -->
<?php if(isset($vaultMessage)): ?>
    <div style="background: red; color: white; padding: 20px; margin: 20px; font-size: 18px; position: fixed; top: 0; left: 0; z-index: 9999; width: 100%;">
        DEBUG: vaultMessage is set: <?php echo e($vaultMessage); ?>

    </div>
<?php else: ?>
    <div style="background: green; color: white; padding: 20px; margin: 20px; font-size: 18px; position: fixed; top: 0; left: 0; z-index: 9999; width: 100%;">
        DEBUG: vaultMessage is NOT set - showing books
    </div>
<?php endif; ?>
    <style>
        /* Vault-specific styles that aren't in the master CSS */
        .vault-literary-quote {
            margin-top: 10px !important;
            margin-bottom: 20px;
        }

        /* Ensure the filter options have proper spacing */
        .vault-filter-options {
            margin-bottom: 1rem !important;
            gap: 10px;
        }
    </style>

    <!-- Flash messages are now handled by master layout -->
    <div class="page-content-container vault-container">
        <section class="vault-hero-section bg-gray d-flex align-items-center justify-content-center vault-padding-medium pb-5">
            <div class="hero-content">
                <div class="container">
                    <div class="row">
                        <div class="text-center vault-padding-medium vault-no-padding-bottom">
                            <?php
                                $quote = App\Helpers\ImageHelper::getRandomQuote();
                                $heroImage = App\Helpers\ImageHelper::getRandomImage('2.Horizontal Small');
                            ?>
                            <div class="d-flex justify-content-center vault-filter-container">
                                <div class="vault-hero-image-container">
                                    <img src="<?php echo e($heroImage['url']); ?>" alt="<?php echo e($heroImage['filename']); ?>" class="img-fluid">
                                    <div class="vault-image-caption"><?php echo e($heroImage['filename']); ?></div>
                                </div>
                            </div>
                            <blockquote class="vault-literary-quote">
                                <?php echo $quote; ?>

                            </blockquote>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="container">
            <div class="row pt-4 mt-4">
                <div class="col-md-12">
                    <div class="container d-flex justify-content-center align-items-center" style="gap:20px;">
                        <form id="filterForm">
                            <?php echo csrf_field(); ?>
                            <!-- Pricing Filter -->
                            <div class="d-flex justify-content-center align-items-center vault-filter-options">
                                <label class="vault-filter-label">
                                    <input type="checkbox" id="freeFilter" name="price_filter" value="free">
                                    Free
                                </label>
                                <label class="vault-filter-label">
                                    <input type="checkbox" id="mostTokensFilter" name="price_filter" value="most_tokens">
                                    Most Tokens
                                </label>
                                <label class="vault-filter-label">
                                    <input type="checkbox" id="verifiedFilter" name="price_filter" value="verified">
                                    Verified Purchase
                                </label>
                                <label class="vault-filter-label">
                                    <input type="checkbox" id="kindleUnlimited" name="price_filter" value="Kindle_Unlimited">
                                    Kindle Unlimited
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
                <?php
                    use App\Models\Review;
                    use App\Models\Reader;
                    use App\Models\Advertising;
                    use Illuminate\Support\Facades\Auth;

                    // Get the current user ID
                    $userId = Auth::user()->id;

                    $uniquePrices = collect();

                    // Get only the books that are actually available to the current user
                    // This means: books that the user hasn't reviewed or read yet
                    $availableBooks = collect();

                    foreach ($allBooks as $book) {
                        $bookId = $book->book->id;

                        $hasReviewed = Review::where('bookId', $bookId)->where('userid', $userId)->exists();

                        $isInReaderList = Reader::where('bookId', $bookId)
                            ->where('userId', $userId)
                            ->where('status', 1)
                            ->exists();

                        if (!$hasReviewed && !$isInReaderList) {
                            $availableBooks->push($book);
                        }
                    }

                    // Extract unique prices from available books
                    $uniquePrices = $availableBooks->pluck('bookPrice')->unique()->values()->all();

                    // Filter out non-VP prices (0 and KU)
                    $vpPrices = array_filter($uniquePrices, function($price) {
                        // Ensure we're comparing strings to strings or numbers to numbers
                        return $price !== '0' && $price !== 0 && $price !== 'KU' && $price !== null;
                    });

                    // Sort the prices numerically for better display
                    usort($vpPrices, function($a, $b) {
                        return (float)$a <=> (float)$b;
                    });

                    // Extract unique categories from available books
                    $uniqueCategories = collect();
                    foreach ($availableBooks as $book) {
                        if ($book->book && $book->book->categories) {
                            $category = $book->book->categories;
                            $categoryArray = [
                                'id' => $category->id,
                                'category' => $category->category,
                            ];

                            if (!$uniqueCategories->contains('id', $category->id)) {
                                $uniqueCategories->push($categoryArray);
                            }
                        }
                    }
                    $uniqueCategories = $uniqueCategories->values()->all();
                ?>

                <div class="col-md-1-5 d-flex flex-column justify-content-start align-items-end vault-sidebar-column vault-price-column">
                    <h5 class="vault-sidebar-heading">Price Range</h5>

                    <?php if(!empty($vpPrices) && count($vpPrices) > 0): ?>
                        <?php $__currentLoopData = $vpPrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $price): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="vault-sidebar-label vault-price-label">
                                <span>$<?php echo e(number_format((float)$price, 2)); ?></span>
                                <input type="checkbox" id="bookPrice_<?php echo e($loop->index); ?>" name="priceFilter"
                                    value="<?php echo e($price); ?>">
                                <label for="bookPrice_<?php echo e($loop->index); ?>"></label>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
                <div class="col-md-9 vault-books-container">
                    <section class="vault-books-section vault-padding-medium" id='MainVaultContainer'>
                        <?php if(isset($vaultMessage)): ?>
                            <div class="alert alert-warning">
                                <strong><?php echo $vaultMessage; ?></strong>
                                <?php if($vaultMessage == "Please upgrade your subscription to get access to Vault!"): ?>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('subscription.plans')); ?>" class="btn btn-primary">Upgrade Subscription</a>
                                    </div>
                                <?php elseif(isset($showUpgradeButton) && $showUpgradeButton): ?>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('subscription.direct-checkout', 'early_bird')); ?>" class="btn btn-primary">Upgrade Subscription</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php elseif($vaultCond->count() == 0): ?>
                            <?php
                                // Check if user has any books at all (regardless of approval status)
                                $userHasAnyBooks = \App\Models\Book::where('publish_by', Auth::user()->id)->exists();
                            ?>
                            
                            <?php if($userHasAnyBooks): ?>
                                
                                <div class="alert alert-warning">
                                    <strong>Dear Auteur, Please Wait For Your 1st Published Book To Be Approved To Get An Access to a Vault</strong>
                                </div>
                            <?php else: ?>
                                
                                <div class="alert alert-warning">
                                    <strong>Please <a href="<?php echo e(route('book.create')); ?>" style="text-decoration: underline; color: inherit;">Publish</a> At least 1 Book as an Auteur To Get Access to a Book Vault</strong>
                                </div>
                            <?php endif; ?>
                        <?php elseif($readCond->count() == $activeAssignmentsCount): ?>
                            <div class="alert alert-warning">
                                <strong>Please Finish Your Current Assignment</strong>
                            </div>
                        <?php else: ?>

                            <div class="row vault-book-container" id="book-container">
                                <?php $__currentLoopData = $allBooks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $book): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $bookIdReview = App\Models\review::where('bookId', $book->book->id)
                                            ->where('userid', Auth::user()->id)
                                            ->value('bookId');

                                        $isBookExistInReader = App\Models\Reader::where('bookId', $book->book->id)
                                            ->where('userId', Auth::user()->id)
                                            ->where('status', 1)
                                            ->value('bookId');
                                    ?>

                                    <?php if($bookIdReview == null && $isBookExistInReader == null): ?>
                                        <div class="col-md-4 vault-book-card-column">
                                            <div class="vault-book h-100">
                                                <div class="card h-100">
                                                    <div class="card-header">
                                                        <img class="img-fluid"
                                                            src="/Books/book_cover/<?php echo e($book->book->front_book_cover); ?>"
                                                            alt="">
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="vault-card-top-section">
                                                            <h4 class="card-title"><?php echo e($book->book->title); ?></h4>
                                                            <p class="book-summary"><?php echo e($book->book->book_summary); ?></p>
                                                        </div>
                                                        <div class="vault-card-middle-section">
                                                            <p><i style="color:red;" class="ri-heart-fill"></i>
                                                                <?php echo e($book->points - $systemPoints); ?>

                                                                Rewards
                                                            </p>
                                                            <?php
                                                                // Convert bookPrice to a number for comparison if it's not 'KU'
                                                                $bookPriceNum = $book->bookPrice === 'KU' ? 'KU' : (float)$book->bookPrice;
                                                            ?>

                                                            <?php if($bookPriceNum === 'KU'): ?>
                                                                <p>Type: <span class="book-type">Kindle Unlimited</span></p>
                                                            <?php elseif($bookPriceNum === 0.0 || $bookPriceNum === 0): ?>
                                                                <p>Type: <span class="book-type">FREE</span></p>
                                                            <?php elseif($bookPriceNum > 0 && $bookPriceNum < 2): ?>
                                                                <p>Type: <span class="book-type">VP $<?php echo e(number_format($bookPriceNum, 2)); ?></span></p>
                                                            <?php else: ?>
                                                                <p>Type: <span class="book-type">$<?php echo e(number_format($bookPriceNum, 2)); ?></span></p>
                                                            <?php endif; ?>
                                                            <?php
                                                                $reviews = App\Models\Review::where('bookId', $book->book->id)->get();
                                                                $totalRating = $reviews->sum('rating');
                                                                $totalReviews = $reviews->count();
                                                                $averageRating = $totalReviews > 0 ? round($totalRating / $totalReviews, 1) : 0;
                                                            ?>
                                                            <p>Time: <span class="turnaround-time"><?php echo e($book->TurnAroundTime ?? 4); ?> <?php echo e(($book->TurnAroundTime == 1) ? 'day' : 'days'); ?></span></p>
                                                            <p>Words: <?php echo e($book->book->wordCount ?? 'N/A'); ?></p>
                                                            <p>Genre: <?php echo e($book->book->categories->category); ?></p>
                                                            <div class="rating">
                                                                <p>Stars:
                                                                    <?php if($totalReviews > 0): ?>
                                                                        <?php echo e($averageRating == (int)$averageRating ? (int)$averageRating : number_format($averageRating, 1)); ?> <i class="ri-star-fill" style="color: #ffd700;"></i>
                                                                        <small>(<?php echo e($totalReviews); ?> <?php echo e(Str::plural('review', $totalReviews)); ?>)</small>
                                                                    <?php else: ?>
                                                                        N/A
                                                                    <?php endif; ?>
                                                                </p>
                                                            </div>

                                                        </div>

                                                        <div class="vault-card-bottom-section">
                                                            <?php
                                                                if ($book->bookPrice == 'KU') {
                                                                    $amount = 'kdp';
                                                                } elseif ($book->bookPrice > 0) {
                                                                    $amount = 'paid';
                                                                } else {
                                                                    $amount = 'free';
                                                                }
                                                            ?>
                                                            <a href="<?php echo e(route('vault.singleBook', ['amount' => $amount, 'slug' => $book->book->slug])); ?>"
                                                                class="btn btn-text-link">View This Book</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </section>
                </div>
                <?php if(!empty($uniqueCategories) && count($uniqueCategories) > 0): ?>
                <div class="col-md-1-5 d-flex flex-column justify-content-start align-items-start vault-sidebar-column vault-category-column">
                    <h5 class="vault-sidebar-heading">Categories</h5>

                    <?php $__currentLoopData = $uniqueCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label for="categoryFilter<?php echo e($category['id']); ?>" class="vault-sidebar-label vault-category-label">
                            <span><?php echo e($category['category']); ?></span>
                            <input type="checkbox" id="categoryFilter<?php echo e($category['id']); ?>" name="categoryFilter"
                                value="<?php echo e($category['id']); ?>">
                        </label>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php endif; ?>
            </div>


            <!-- Your Blade view file -->


            <!-- Display the filtered books here -->
            <div id="filteredBooks">
                <!-- Display books dynamically using JavaScript -->
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>




<?php $__env->startSection('additionalScript'); ?>
    <script>
        // Vault filtering and sorting JavaScript

        $(document).ready(function() {
            // Define the filtering function
            function applyFilters() {
                // Get all price filter checkboxes (Free, Most Tokens, Verified Purchase, Kindle Unlimited)
                var priceFilters = $('input[name="price_filter"]:checked').map(function() {
                    return this.value;
                }).get();

                var typeFilters = $('input[name="type_filter"]:checked').map(function() {
                    return this.value;
                }).get();

                // Get category filter checkboxes
                var categoryFilters = $('input[name="categoryFilter"]:checked').map(function() {
                    return this.value;
                }).get();

                // Get price range checkboxes (specific dollar amounts)
                var priceRangeFilters = $('input[name="priceFilter"]:checked').map(function() {
                    return this.value;
                }).get();

                // Log the selected filters for debugging
                console.log('Applying filters:', {
                    priceFilters: priceFilters,
                    categoryFilters: categoryFilters,
                    priceRangeFilters: priceRangeFilters
                });

                // Clear any existing error messages before making a new request
                $('#error').remove();

                // Get the CSRF token from the meta tag
                var csrfToken = $('meta[name="csrf-token"]').attr('content');

                // Show loading indicator
                $('#MainVaultContainer').append('<div id="loading" class="text-center my-4"><i class="ri-loader-4-line fa-spin"></i> Loading...</div>');

                // Store the current filter state for comparison
                window.lastFilters = {
                    type_filter: priceFilters,
                    category_filter: categoryFilters,
                    price_range_filter: priceRangeFilters
                };

                // Make Ajax request to fetch filtered books with CSRF token included
                $.ajax({
                    url: '/filter-books',
                    type: 'POST',
                    data: {
                        type_filter: priceFilters, // This includes free, most_tokens, verified, Kindle_Unlimited
                        category_filter: categoryFilters,
                        price_range_filter: priceRangeFilters
                    },
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    success: function(data) {
                        console.log('Filter response:', data);

                        // Remove loading indicator
                        $('#loading').remove();

                        // Handle error messages
                        if (data.Message) {
                            if ($('#error').length === 0) { // Check if error message doesn't already exist
                                $('.vault-book-container').html('');

                                // Create a more helpful error message that includes the applied filters
                                var filterInfo = '';
                                if (priceFilters.length > 0) {
                                    filterInfo += '<br>Type filters: ' + priceFilters.join(', ');
                                }
                                if (categoryFilters.length > 0) {
                                    filterInfo += '<br>Category filters: ' + categoryFilters.join(', ');
                                }
                                if (priceRangeFilters.length > 0) {
                                    filterInfo += '<br>Price filters: $' + priceRangeFilters.join(', $');
                                }

                                var errorHtml = `<div id='error' class="alert alert-warning alert-block">
                                    ${data.Message}
                                    ${filterInfo ? '<br><small>Applied filters: ' + filterInfo + '</small>' : ''}
                                    <br><small>Try removing some filters to see more books.</small>
                                </div>`;
                                $('#MainVaultContainer').append(errorHtml);
                            }
                        } else {
                            $('#error').remove(); // Remove error message if it exists
                        }



                        // Clear existing content in the book container
                        $('.vault-book-container').html('');



                        // Iterate through each book in the response and append its HTML to the container
                        $.each(data.books, function(index, book) {
                            var amount;
                            if (book.bookPrice == 'KU') {
                                amount = 'kdp';
                            } else if (book.bookPrice > 0) {
                                amount = 'paid';
                            } else {
                                amount = 'free';
                            }

                            var TotalAmount;
                            var bookPoints;
                            // Convert bookPrice to a number for comparison if it's not 'KU'
                            var bookPriceNum = book.bookPrice === 'KU' ? 'KU' : parseFloat(book.bookPrice);

                            if (bookPriceNum === 'KU') {
                                TotalAmount = '<span class="book-type">Kindle Unlimited</span>';
                                bookPoints = book.points;
                            } else if (bookPriceNum === 0 || bookPriceNum === 0.0) {
                                TotalAmount = '<span class="book-type">FREE</span>';
                                bookPoints = book.points;
                            } else if (bookPriceNum > 0 && bookPriceNum < 2) {
                                // Format to ensure consistent decimal places
                                TotalAmount = '<span class="book-type">VP $' + bookPriceNum.toFixed(2) + '</span>';
                                bookPoints = book.points;
                            } else {
                                TotalAmount = '<span class="book-type">$' + bookPriceNum.toFixed(2) + '</span>';
                                bookPoints = book.points;
                            }

                            var bookHtml = `
                        <div class="col-md-4 vault-book-card-column">
                            <div class="vault-book h-100">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <img class="img-fluid" src="/Books/book_cover/${book.book.front_book_cover}" alt="">
                                    </div>
                                    <div class="card-body">
                                        <div class="vault-card-top-section">
                                            <h4 class="card-title">${book.book.title}</h4>
                                            <p class="book-summary">${book.book.book_summary}</p>
                                        </div>
                                        <div class="vault-card-middle-section">
                                            <p><i style="color:red;" class="ri-heart-fill"></i> ${book.points - data.systemPoints} Rewards</p>
                                            <p>Type: ${TotalAmount}</p>
                                            <p>Time: <span class="turnaround-time">${book.TurnAroundTime ?? 4} ${book.TurnAroundTime == 1 ? 'day' : 'days'}</span></p>
                                            <p>Words: ${book.book.wordCount ?? 'N/A'}</p>
                                            <p>Genre: ${book.book.categories ? book.book.categories.category : 'N/A'}</p>
                                            <div class="rating">
                                                <p>Stars: ${(() => {
                                                    const reviews = book.book.reviews || [];
                                                    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
                                                    const totalReviews = reviews.length;
                                                    if (totalReviews > 0) {
                                                        const avgRating = totalRating / totalReviews;
                                                        // Check if the rating is an integer
                                                        const formattedRating = Number.isInteger(avgRating) ? avgRating : avgRating.toFixed(1);
                                                        return `${formattedRating} <i class="ri-star-fill" style="color: #ffd700;"></i> <small>(${totalReviews} ${totalReviews === 1 ? 'review' : 'reviews'})</small>`;
                                                    }
                                                    return 'N/A';
                                                })()}</p>
                                            </div>
                                        </div>
                                        <div class="vault-card-bottom-section">
                                            <a href="/view-book/${amount}/${book.book.slug}" class="btn btn-text-link">View This Book</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                            // Append the book HTML to the container
                            $('.vault-book-container').append(bookHtml);
                        });
                    },
                    error: function(error) {
                        console.log(error);
                        // Remove loading indicator
                        $('#loading').remove();

                        // Show error message
                        $('.vault-book-container').html('');
                        var errorHtml = `<div id='error' class="alert alert-danger alert-block">
                            An error occurred while filtering books. Please try again.
                        </div>`;
                        $('#MainVaultContainer').append(errorHtml);
                    }
                });
            }

            // Attach the filtering function to the change event of checkboxes
            $('input[type="checkbox"]').on('change', applyFilters);
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/dashboard/vault.blade.php ENDPATH**/ ?>