@extends('master-layout.master-layout')
@section('page_title', 'Auteur')
@section('page_content')
    <div class="page-content-container">


        <section class="author-hero-section bg-gray d-flex align-items-center justify-content-center author-padding-medium pb-5">
            <div class="hero-content">
                <div class="container">
                    <div class="row">
                        <div class="text-center author-padding-medium author-no-padding-bottom">
                            @php
                                $quote = App\Helpers\ImageHelper::getRandomQuote();
                                $heroImage = App\Helpers\ImageHelper::getRandomImage('3.Square');
                            @endphp
                            <div class="d-flex justify-content-center author-filter-container">
                                <div class="author-hero-image-container">
                                    <img src="{{ $heroImage['url'] }}" alt="{{ $heroImage['filename'] }}" class="img-fluid">
                                    <div class="author-image-caption">{{ $heroImage['filename'] }}</div>
                                </div>
                            </div>
                            <blockquote class="author-literary-quote">
                                {!! $quote !!}
                            </blockquote>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="container">
            <div class="text-left">
                <!--<a href="{{ route('book.create') }}" class="btn btn-text-link mt-0 mb-4" style="font-size: 1.3rem !important;">Publish A Book</a>-->
                @php
                    $user = Auth::user();
                    $userBookCount = \App\Models\Book::where('publish_by', $user->id)->count();
                    $isFirstBook = $userBookCount == 0;
                    $hasAmazonReviewerName = !empty($user->amazon_reviewer_name);
                @endphp
                
                @if($isFirstBook && !$hasAmazonReviewerName)
                    <a href="{{ route('profile.index') }}?redirect_to_book_create=1" class="btn btn-sm btn-primary">Publish A Book</a>
                @else
                    <a href="{{ route('book.create') }}" class="btn btn-sm btn-primary">Publish A Book</a>
                @endif
            </div>
            <section class="author-padding-medium">
                <div class="container bg-light p-4">
                    <h3 class="page_section_heading author-section-heading">Your Books</h3>
                    <div class="row author-book-container">
                        @foreach ($books as $book)
                            <div class="col-md-12 mt-4 author-book-card-column">
                                <div class="author-book h-100">
                                    <div class="card h-100">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="card-header">
                                                    <img src="Books/book_cover/{{ $book->front_book_cover }}" alt="{{ $book->title }}" class="img-fluid">
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="card-body">
                                                    <div class="author-card-top-section">
                                                        <h4 class="card-title">{{ $book->title }}</h4>
                                                        <p>{{ $book->book_summary }}</p>
                                                    </div>
                                                    <div class="author-card-middle-section">
                                                        @php
                                                            if ($book->approval_status == 1) {
                                                                $status = '<span class="text-success">Active</span>';
                                                            } elseif ($book->approval_status == 2) {
                                                                $status = '<span class="text-danger">Changes Required</span>';
                                                            } else {
                                                                $status = '<span class="text-warning">Pending Approval</span>';
                                                            }
                                                        @endphp
                                                        <p>Book Status: {!! $status !!}</p>
                                                    </div>
                                                    <div class="author-card-bottom-section">
                                                        <a href="{{ route('books.single', $book->slug) }}" class="btn btn-text-link">View Book</a>
                                                        @php
                                                            $user = Auth::user();
                                                            
                                                            // Check if Reviews on Hold system is enabled
                                                            $reviewsOnHold = \App\Models\SystemControl::where('key_type', 'reviews_on_hold')->first();
                                                            $isReviewsOnHoldEnabled = $reviewsOnHold && $reviewsOnHold->value == 1;
                                                            
                                                            $isWaitingForReview = $user->waiting_for_first_review_approval === true;
                                                            
                                                            // Check if user has any approved reviews (including partial validation)
                                                            $hasApprovedReview = \App\Models\Review::where('userid', $user->id)
                                                                ->whereIn('reviewStatus', [1, 2])
                                                                ->exists();
                                                            
                                                            // Auto-fix for existing users: Check if user has featured books but flag is still false
                                                            if ($isReviewsOnHoldEnabled && !$hasApprovedReview && !$user->has_used_first_feature) {
                                                                // Check if user has any advertising/feature history
                                                                $hasFeatureHistory = \App\Models\Advertising::where('uid', $user->id)->exists();
                                                                
                                                                if ($hasFeatureHistory) {
                                                                    // User has featured before but flag wasn't set - fix it now
                                                                    $user->has_used_first_feature = true;
                                                                    $user->save();
                                                                }
                                                            }
                                                            
                                                            // Refresh user to get updated flag value
                                                            $user->refresh();
                                                            
                                                            // New logic: If Reviews on Hold is enabled AND user has no approved reviews AND has used their first feature, disable
                                                            $shouldDisableFeature = $isReviewsOnHoldEnabled && !$hasApprovedReview && $user->has_used_first_feature;
                                                        @endphp
                                                        
                                                        @if($shouldDisableFeature)
                                                            <span class="btn btn-text-link text-muted" style="cursor: not-allowed;">Feature a Book!</span>
                                                        @else
                                                            <a href="{{ route('books.single', $book->slug) }}#review-options" class="btn btn-text-link">Feature a Book!</a>
                                                        @endif
                                                        @if ($book->approval_status == 0)
                                                            <a href="{{ route('authorEdit.single', $book->slug) }}" class="btn btn-text-link">Edit Book</a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        </div>
    </div>
@endsection
