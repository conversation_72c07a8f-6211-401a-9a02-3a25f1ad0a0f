
<?php $__env->startSection('page_title', 'Auteur'); ?>
<?php $__env->startSection('page_content'); ?>
    <div class="page-content-container">


        <section class="author-hero-section bg-gray d-flex align-items-center justify-content-center author-padding-medium pb-5">
            <div class="hero-content">
                <div class="container">
                    <div class="row">
                        <div class="text-center author-padding-medium author-no-padding-bottom">
                            <?php
                                $quote = App\Helpers\ImageHelper::getRandomQuote();
                                $heroImage = App\Helpers\ImageHelper::getRandomImage('3.Square');
                            ?>
                            <div class="d-flex justify-content-center author-filter-container">
                                <div class="author-hero-image-container">
                                    <img src="<?php echo e($heroImage['url']); ?>" alt="<?php echo e($heroImage['filename']); ?>" class="img-fluid">
                                    <div class="author-image-caption"><?php echo e($heroImage['filename']); ?></div>
                                </div>
                            </div>
                            <blockquote class="author-literary-quote">
                                <?php echo $quote; ?>

                            </blockquote>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="container">
            <div class="text-left">
                <!--<a href="<?php echo e(route('book.create')); ?>" class="btn btn-text-link mt-0 mb-4" style="font-size: 1.3rem !important;">Publish A Book</a>-->
                <?php
                    $user = Auth::user();
                    $userBookCount = \App\Models\Book::where('publish_by', $user->id)->count();
                    $isFirstBook = $userBookCount == 0;
                    $hasAmazonReviewerName = !empty($user->amazon_reviewer_name);
                ?>
                
                <?php if($isFirstBook && !$hasAmazonReviewerName): ?>
                    <a href="<?php echo e(route('profile.index')); ?>?redirect_to_book_create=1" class="btn btn-sm btn-primary">Publish A Book</a>
                <?php else: ?>
                    <a href="<?php echo e(route('book.create')); ?>" class="btn btn-sm btn-primary">Publish A Book</a>
                <?php endif; ?>
            </div>
            <section class="author-padding-medium">
                <div class="container bg-light p-4">
                    <h3 class="page_section_heading author-section-heading">Your Books</h3>
                    <div class="row author-book-container">
                        <?php $__currentLoopData = $books; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $book): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-12 mt-4 author-book-card-column">
                                <div class="author-book h-100">
                                    <div class="card h-100">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="card-header">
                                                    <img src="Books/book_cover/<?php echo e($book->front_book_cover); ?>" alt="<?php echo e($book->title); ?>" class="img-fluid">
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="card-body">
                                                    <div class="author-card-top-section">
                                                        <h4 class="card-title"><?php echo e($book->title); ?></h4>
                                                        <p><?php echo e($book->book_summary); ?></p>
                                                    </div>
                                                    <div class="author-card-middle-section">
                                                        <?php
                                                            if ($book->approval_status == 1) {
                                                                $status = '<span class="text-success">Active</span>';
                                                            } elseif ($book->approval_status == 2) {
                                                                $status = '<span class="text-danger">Changes Required</span>';
                                                            } else {
                                                                $status = '<span class="text-warning">Pending Approval</span>';
                                                            }
                                                        ?>
                                                        <p>Book Status: <?php echo $status; ?></p>
                                                    </div>
                                                    <div class="author-card-bottom-section">
                                                        <a href="<?php echo e(route('books.single', $book->slug)); ?>" class="btn btn-text-link">View Book</a>
                                                        <?php
                                                            $user = Auth::user();
                                                            
                                                            // Check if Reviews on Hold system is enabled
                                                            $reviewsOnHold = \App\Models\SystemControl::where('key_type', 'reviews_on_hold')->first();
                                                            $isReviewsOnHoldEnabled = $reviewsOnHold && $reviewsOnHold->value == 1;
                                                            
                                                            $isWaitingForReview = $user->waiting_for_first_review_approval === true;
                                                            
                                                            // Check if user has any approved reviews (including partial validation)
                                                            $hasApprovedReview = \App\Models\Review::where('userid', $user->id)
                                                                ->whereIn('reviewStatus', [1, 2])
                                                                ->exists();
                                                            
                                                            // Auto-fix for existing users: Check if user has featured books but flag is still false
                                                            if ($isReviewsOnHoldEnabled && !$hasApprovedReview && !$user->has_used_first_feature) {
                                                                // Check if user has any advertising/feature history
                                                                $hasFeatureHistory = \App\Models\Advertising::where('uid', $user->id)->exists();
                                                                
                                                                if ($hasFeatureHistory) {
                                                                    // User has featured before but flag wasn't set - fix it now
                                                                    $user->has_used_first_feature = true;
                                                                    $user->save();
                                                                }
                                                            }
                                                            
                                                            // Refresh user to get updated flag value
                                                            $user->refresh();
                                                            
                                                            // New logic: If Reviews on Hold is enabled AND user has no approved reviews AND has used their first feature, disable
                                                            $shouldDisableFeature = $isReviewsOnHoldEnabled && !$hasApprovedReview && $user->has_used_first_feature;
                                                        ?>
                                                        
                                                        <?php if($shouldDisableFeature): ?>
                                                            <span class="btn btn-text-link text-muted" style="cursor: not-allowed;">Feature a Book!</span>
                                                        <?php else: ?>
                                                            <a href="<?php echo e(route('books.single', $book->slug)); ?>#review-options" class="btn btn-text-link">Feature a Book!</a>
                                                        <?php endif; ?>
                                                        <?php if($book->approval_status == 0): ?>
                                                            <a href="<?php echo e(route('authorEdit.single', $book->slug)); ?>" class="btn btn-text-link">Edit Book</a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </section>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/dashboard/author.blade.php ENDPATH**/ ?>