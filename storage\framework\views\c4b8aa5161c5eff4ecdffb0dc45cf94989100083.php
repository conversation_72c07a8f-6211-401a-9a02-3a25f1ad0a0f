
<?php if(auth()->guard()->check()): ?>
    <?php
        $user = Auth::user();
        $userFlashKey = 'user_flash_message_' . $user->id;
        $userFlashMessage = Session::get($userFlashKey);
        
        // Check for congratulations activity notification
        $congratsActivity = \App\Models\Activity::where('userid', $user->id)
            ->where('reason', 'like', 'Congratulations! Your first review got%')
            ->where('read', 0)
            ->first();
        
        $showCongratulations = $congratsActivity ? true : false;
    ?>
    
    
    <?php if($userFlashMessage): ?>
        <div class="alert alert-<?php echo e($userFlashMessage['type'] ?? 'success'); ?>">
            <?php echo $userFlashMessage['message']; ?>

        </div>
        <?php
            // Remove the message after displaying it
            Session::forget($userFlashKey);
            if (config('app.debug')) {
                \Log::info("Session flash message displayed and removed for user " . $user->id);
            }
        ?>
    <?php endif; ?>
    
    
    <?php if($showCongratulations): ?>
        <div class="alert alert-success">
            Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)
        </div>
        <?php
            // Mark the activity as read after displaying the message
            $congratsActivity->read = 1;
            $congratsActivity->save();
        ?>
    <?php endif; ?>
<?php endif; ?>

<?php if($message = Session::get('success')): ?>
    
    <?php if(strpos($message, 'quiz-results') === false): ?>
        <div class="alert alert-success">
            <?php echo $message; ?>

        </div>
    <?php endif; ?>
<?php endif; ?>

<?php if($message = Session::get('core_auteur_message')): ?>
    <div class="alert alert-info" style="background-color: #e0f7fa; border-color: #b2ebf2; color: #007bff;">
        <?php echo $message; ?>

    </div>
<?php endif; ?>

<?php if($message = Session::get('welcome_message')): ?>
    <div class="alert alert-info">
        <?php echo $message; ?>

    </div>
<?php endif; ?>

<?php if($message = Session::get('error')): ?>
    <div class="alert alert-danger">
        <?php echo $message; ?>

    </div>
<?php endif; ?>

<?php if($message = Session::get('info')): ?>
    <div class="alert alert-info">
        <?php echo $message; ?>

    </div>
<?php endif; ?>

<?php if($message = Session::get('warning')): ?>
    <div class="alert alert-warning">
        <?php echo $message; ?>

    </div>
<?php endif; ?>
<?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/includes/flash-messages.blade.php ENDPATH**/ ?>