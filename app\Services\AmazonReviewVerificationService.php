<?php

namespace App\Services;

use App\Models\Book;
use App\Models\Review;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Activity;
use App\Models\SystemControl;
use App\Models\Advertising;
use App\Models\Reader;
use App\Models\WalletTransaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AmazonReviewVerificationService
{
    /**
     * Amazon credentials
     */
    private $amazonEmail = '<EMAIL>';
    private $amazonPassword = 'Dazain_0001';

    /**
     * Amazon API credentials
     */
    private $apiKey = 'EB68D8B1A4A94887BABF350D75F82CBF';
    private $apiBaseUrl = 'https://api.asindataapi.com/request';
    private $partnerTag;
    private $region;

    /**
     * Oxylabs API credentials
     */
    private $oxyUsername = 'stoicputto_AympJ';
    private $oxyPassword = 'Dazain_0Dazain_0';
    private $oxyApiUrl = 'https://realtime.oxylabs.io/v1/queries';

    public function __construct()
    {
        $this->partnerTag = config('services.amazon.partner_tag');
        $this->region = config('services.amazon.region', 'us');
    }

    /**
     * Extract ASIN from Amazon URL
     *
     * @param string $amazonUrl
     * @return string|null
     */
    public function extractASIN($amazonUrl)
    {
        // Check for DP pattern
        if (preg_match('/\/dp\/([A-Z0-9]{10})/', $amazonUrl, $matches)) {
            return $matches[1];
        }

        // Alternative pattern check for product pages
        if (preg_match('/\/product\/([A-Z0-9]{10})/', $amazonUrl, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Get Amazon reviews page URL
     *
     * @param string $asin
     * @param int $page
     * @return string
     */
    private function getReviewsPageUrl($asin, $page = 1)
    {
        return "https://www.amazon.com/product-reviews/{$asin}/ref=cm_cr_arp_d_viewopt_srt?ie=UTF8&reviewerType=all_reviews&sortBy=recent&pageNumber={$page}";
    }

    /**
     * Get Amazon product page URL
     *
     * @param string $asin
     * @return string
     */
    private function getProductPageUrl($asin)
    {
        return "https://www.amazon.com/dp/{$asin}/";
    }

    /**
     * Make HTTP request with retry logic and rotating user agents
     *
     * @param string $url
     * @param int $maxRetries
     * @return \Illuminate\Http\Client\Response
     * @throws \Exception
     */
    private function makeRequest($url, $maxRetries = 3)
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/114.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Edg/114.0.1823.58',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
        ];

        $attempt = 1;
        $lastException = null;

        Log::info("Making HTTP request to URL: {$url}");

        while ($attempt <= $maxRetries) {
            $userAgent = $userAgents[array_rand($userAgents)];
            Log::info("HTTP request attempt {$attempt}/{$maxRetries} with User-Agent: {$userAgent}");

            try {
                // Add random delay between attempts (increasing with each retry)
                if ($attempt > 1) {
                    $delay = rand(2, 5) * $attempt;
                    Log::info("Waiting {$delay} seconds before retry attempt #{$attempt}");
                    sleep($delay);
                }

                // Set up headers with random user agent
                $headers = [
                    'User-Agent' => $userAgent,
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1',
                    'Cache-Control' => 'no-cache',
                    'Pragma' => 'no-cache'
                ];

                // Add a randomized timeout to avoid being detected as a bot
                $timeout = rand(10, 30);

                $response = Http::withHeaders($headers)
                    ->timeout($timeout)
                    ->get($url);

                $statusCode = $response->status();
                Log::info("HTTP response status code: {$statusCode}");

                // Handle specific status codes
                if ($statusCode === 200) {
                    // Success
                    Log::info("Request successful with size: " . strlen($response->body()) . " bytes");

                    // Check for CAPTCHA or robot check page
                    $body = $response->body();
                    if (
                        stripos($body, 'captcha') !== false ||
                        stripos($body, 'robot check') !== false ||
                        stripos($body, 'verify you\'re a human') !== false
                    ) {
                        Log::warning("CAPTCHA or robot verification detected in response");
                        throw new \Exception("Amazon is requiring CAPTCHA verification");
                    }

                    return $response;
                } elseif ($statusCode === 503 || $statusCode === 403) {
                    // Service Unavailable or Forbidden - likely rate limited or blocked
                    Log::warning("Request blocked with status code: {$statusCode}. Might be rate limited.");
                    $lastException = new \Exception("Amazon request blocked with status {$statusCode}");
                } elseif ($statusCode === 302 || $statusCode === 301) {
                    // Handle redirects manually if needed
                    $location = $response->header('Location');
                    Log::info("Redirect detected to: {$location}");

                    if ($location) {
                        Log::info("Following redirect to: {$location}");
                        return $this->makeRequest($location, $maxRetries - $attempt + 1);
                    }

                    // If we can't follow redirect, return the response anyway
                    return $response;
                } else {
                    // Other error status codes
                    Log::warning("Request failed with status: {$statusCode}");
                    $lastException = new \Exception("Amazon request failed with status {$statusCode}");
                }

                $attempt++;
            } catch (\Exception $e) {
                Log::error("HTTP request attempt {$attempt} failed with error: " . $e->getMessage());
                $lastException = $e;
                $attempt++;
            }
        }

        // If we got here, all attempts failed
        $errorMessage = $lastException ? $lastException->getMessage() : "Unknown error";
        Log::error("Failed to fetch Amazon page after {$maxRetries} attempts. Last error: {$errorMessage}");
        throw new \Exception("Failed to fetch Amazon page after {$maxRetries} attempts: {$errorMessage}");
    }

    /**
     * Authenticate with Amazon and make HTTP request
     */
    private function makeAuthenticatedRequest($url, $maxRetries = 3)
    {
        try {
            Log::info('Making authenticated request to Amazon: ' . $url);

            // First, attempt to login to Amazon
            $loginUrl = 'https://www.amazon.com/ap/signin';

            // Get the initial login page to obtain tokens and cookies
            $loginPageResponse = $this->makeRequest($loginUrl);

            if (!$loginPageResponse->successful()) {
                throw new \Exception('Failed to load Amazon login page');
            }

            // Extract forms and tokens from the login page (this is simplified)
            $html = $loginPageResponse->body();
            $cookies = $loginPageResponse->cookies();

            // Extract CSRF token and other form fields (implementation will depend on Amazon's current form structure)
            preg_match('/name="appActionToken" value="([^"]+)"/', $html, $appTokenMatches);
            preg_match('/name="appAction" value="([^"]+)"/', $html, $appActionMatches);
            preg_match('/name="workflowState" value="([^"]+)"/', $html, $workflowMatches);

            $appActionToken = $appTokenMatches[1] ?? '';
            $appAction = $appActionMatches[1] ?? '';
            $workflowState = $workflowMatches[1] ?? '';

            // Prepare login form data
            $formData = [
                'appAction' => $appAction,
                'appActionToken' => $appActionToken,
                'workflowState' => $workflowState,
                'email' => $this->amazonEmail,
                'password' => $this->amazonPassword,
                'create' => '0',
                'rememberMe' => 'true'
            ];

            // Attempt to login
            $loginResponse = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Origin' => 'https://www.amazon.com',
                'Referer' => $loginUrl
            ])->withCookies(
                $cookies->toArray(),
                'amazon.com'
            )->post($loginUrl, $formData);

            // Check if login successful (redirect to homepage or dashboard)
            if ($loginResponse->successful() &&
                (strpos($loginResponse->effectiveUri(), 'signin') === false ||
                 strpos($loginResponse->body(), 'Hello, ') !== false)) {

                Log::info('Successfully logged in to Amazon');
                $authenticatedCookies = $loginResponse->cookies();

                // Now make the actual product page request with the authenticated cookies
                $productResponse = Http::withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Referer' => 'https://www.amazon.com/'
                ])->withCookies(
                    $authenticatedCookies->toArray(),
                    'amazon.com'
                )->get($url);

                if ($productResponse->successful()) {
                    return $productResponse;
                }
            }

            // If login failed or product request failed, fall back to regular request
            Log::warning('Amazon authentication failed, falling back to regular request');
            return $this->makeRequest($url, $maxRetries);

        } catch (\Exception $e) {
            Log::error('Failed to make authenticated request: ' . $e->getMessage());

            // Fall back to regular request
            return $this->makeRequest($url, $maxRetries);
        }
    }

    /**
     * Extract actual review link from Amazon book page HTML
     *
     * @param string $html
     * @param string $reviewerName
     * @return string|null
     */
    public function extractReviewLink($html, $reviewerName)
    {
        try {
            Log::info('Extracting review link for reviewer: ' . $reviewerName);

            // Pattern to find review links in the HTML
            $pattern = '/<a data-hook="review-title" class="a-size-base a-link-normal review-title a-color-base review-title-content a-text-bold" href="([^"]+)"/';

            if (preg_match_all($pattern, $html, $matches)) {
                Log::info('Found ' . count($matches[1]) . ' review links on the page');

                // Check each review to find one from our reviewer
                foreach ($matches[1] as $rawLink) {
                    // Construct the full Amazon URL by adding domain
                    $fullLink = "https://www.amazon.com" . $rawLink;
                    Log::info('Potential review link: ' . $fullLink);

                    // If we have reviewer name, check if this review belongs to them
                    // This would require fetching each review page to check the reviewer name
                    // For now, we'll return the first link found
                    return $fullLink;
                }
            }

            Log::warning('No review links found in the HTML');
            return null;
        } catch (\Exception $e) {
            Log::error('Error extracting review link: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if a review exists on Amazon
     *
     * @param \App\Models\Review $review
     * @return array
     */
    public function checkReviewExists(Review $review)
    {
        try {
            Log::info('Starting direct review verification for Review ID: ' . $review->id);

            $book = Book::findOrFail($review->bookId);

            // Fix the case sensitivity issue and handle different field name variations
            // Safely get user without assuming field names
            $userId = $review->userId ?? $review->userid ?? null;
            if (!$userId) {
                Log::error('Review has no userId or userid field: ' . $review->id);
                return [
                    'success' => false,
                    'message' => 'Review has invalid user reference',
                ];
            }

            $user = User::find($userId);
            if (!$user) {
                Log::error('User not found for ID: ' . $userId . ' in review: ' . $review->id);
                return [
                    'success' => false,
                    'message' => 'User not found for review',
                ];
            }

            Log::info('Checking review for Book: ' . $book->title . ', Reviewer: ' . $user->amazon_reviewer_name);

            if (!$book->book_amazon_url) {
                Log::warning('Book has no Amazon URL, Book ID: ' . $book->id);
                return [
                    'success' => false,
                    'message' => 'Book has no Amazon URL',
                ];
            }

            if (!$user->amazon_reviewer_name) {
                Log::warning('User has no Amazon reviewer name, User ID: ' . $user->id);
                return [
                    'success' => false,
                    'message' => 'User has no Amazon reviewer name',
                ];
            }

            // Extract ASIN from book URL
            $asin = $this->extractASIN($book->book_amazon_url);
            if (!$asin) {
                Log::warning('Could not extract ASIN from book URL: ' . $book->book_amazon_url);
                return [
                    'success' => false,
                    'message' => 'Invalid Amazon book URL',
                ];
            }

            Log::info('Extracted ASIN: ' . $asin . ' for book: ' . $book->title);

            // First, check the main product page (often has most recent reviews)
            $productPageUrl = $this->getProductPageUrl($asin);
            Log::info('Checking main product page URL: ' . $productPageUrl);

            try {
                $response = $this->makeRequest($productPageUrl);
                $html = $response->body();

                // Process the product page to find matching reviews
                $reviews = $this->parseReviewsFromHtml($html, $user->amazon_reviewer_name);
                Log::info('Main product page search found ' . count($reviews) . ' reviews');

                if (!empty($reviews)) {
                    $matchedReview = $reviews[0]; // Take the first matching review

                    return [
                        'success' => true,
                        'message' => 'Review found on main product page',
                        'verified_purchase' => $matchedReview['verified_purchase'],
                        'review_link' => $matchedReview['review_link'] ?? null
                    ];
                }
            } catch (\Exception $e) {
                Log::warning('Error checking main product page: ' . $e->getMessage() . '. Trying reviews page instead.');
                // Continue to reviews page search
            }

            // If no match found on product page, check the dedicated reviews page
            $reviewsPageUrl = $this->getReviewsPageUrl($asin);
            Log::info('Checking reviews page URL: ' . $reviewsPageUrl);

            try {
                $response = $this->makeRequest($reviewsPageUrl);
                $html = $response->body();

                // Extract actual review link
                $reviewLink = $this->extractReviewLink($html, $user->amazon_reviewer_name);

                // Process the reviews page to find matching reviews
                $reviews = $this->parseReviewsFromHtml($html, $user->amazon_reviewer_name);

                Log::info('Reviews page search found ' . count($reviews) . ' reviews');

                if (!empty($reviews)) {
                    $matchedReview = $reviews[0]; // Take the first matching review

                    return [
                        'success' => true,
                        'message' => 'Review found on reviews page',
                        'verified_purchase' => $matchedReview['verified_purchase'],
                        'review_link' => $reviewLink ?? $matchedReview['review_link'] ?? null
                    ];
                }

                // If no reviews found on first page, check next pages (up to 3 pages)
                for ($page = 2; $page <= 3; $page++) {
                    $nextPageUrl = $this->getReviewsPageUrl($asin, $page);
                    Log::info('Checking next page: ' . $nextPageUrl);

                    $response = $this->makeRequest($nextPageUrl);
                    $html = $response->body();

                    // Extract actual review link from this page too
                    $pageReviewLink = $this->extractReviewLink($html, $user->amazon_reviewer_name);
                    if ($pageReviewLink) {
                        $reviewLink = $pageReviewLink;
                    }

                    $moreReviews = $this->parseReviewsFromHtml($html, $user->amazon_reviewer_name);

                    if (!empty($moreReviews)) {
                        $matchedReview = $moreReviews[0]; // Take the first matching review

                        return [
                            'success' => true,
                            'message' => 'Review found on page ' . $page,
                            'verified_purchase' => $matchedReview['verified_purchase'],
                            'review_link' => $reviewLink ?? $matchedReview['review_link'] ?? null
                        ];
                    }
                }

                Log::info('No matching reviews found for reviewer: ' . $user->amazon_reviewer_name);
                return [
                    'success' => false,
                    'message' => 'Review not found',
                ];

            } catch (\Exception $e) {
                Log::error('Error fetching reviews page: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                return [
                    'success' => false,
                    'message' => 'Error fetching reviews: ' . $e->getMessage(),
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception in checkReviewExists: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return [
                'success' => false,
                'message' => 'Error checking review: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Auto-approve a verified review
     *
     * @param \App\Models\Review $review
     * @return array
     */
    public function autoApproveReview(Review $review)
    {
        try {
            $result = $this->checkReviewExists($review);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'action' => 'none',
                    'message' => $result['message']
                ];
            }

            $book = Book::find($review->bookId);
            // Safely handle both userId and userid fields
            $currentUser = $review->userId ?? $review->userid;

            // For Paid reviews, check VP status
            if ($review->type == 'Paid') {
                if (!$result['verified_purchase']) {
                    try {
                        // Partial approval - review exists but not verified purchase
                        $this->partiallyApproveReview($review, $result);

                        Log::info("Review {$review->id} found but not VP, partially approved");
                        return [
                            'success' => true,
                            'action' => 'partial_approval',
                            'message' => 'Review found but not verified purchase, partially approved'
                        ];
                    } catch (\Exception $e) {
                        Log::error("Error in partial approval: " . $e->getMessage());
                        // Set status to partially approved (2) even if wallet adjustment failed
                        // This ensures the review is at least marked as found but not VP
                        $review->update(['reviewStatus' => 2]);

                        return [
                            'success' => true,
                            'action' => 'partial_approval_error',
                            'message' => 'Review partially approved, but error adjusting balances: ' . $e->getMessage()
                        ];
                    }
                }
            }

            // Full approval - review exists and meets all criteria
            $updateData = ['reviewStatus' => 1];

            // Save the review link if available
            if (!empty($result['review_link'])) {
                $updateData['review_link'] = $result['review_link'];
                Log::info("Saving review link during approval: " . $result['review_link']);
            }

            $review->update($updateData);

            // Handle Reviews on Hold logic - unlock features if this is user's first approved review
            $user = User::find($currentUser);
            if ($user && ($user->waiting_for_first_review_approval ?? false)) {
                // Check if this is their first approved review (including partial validation)
                $approvedReviewsCount = Review::where('userid', $currentUser)
                    ->whereIn('reviewStatus', [1, 2])
                    ->count();
                
                if ($approvedReviewsCount == 1) { // This is their first approved review
                    // Unlock the user's features and reset tracking flags
                    $user->waiting_for_first_review_approval = false;
                    $user->has_used_first_feature = false;
                    $user->has_taken_first_vault_book = false;
                    $user->save();
                    
                    // Create a congratulatory activity
                    $congratsActivity = new Activity;
                    $congratsActivity->userId = $currentUser;
                    $congratsActivity->bookId = $review->bookId;
                    $congratsActivity->reason = "Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)";
                    $congratsActivity->save();
                    
                    // Store a flash message for the user to see on their next page visit
                    $flashKey = "user_flash_message_{$currentUser}";
                    $flashMessage = [
                        'type' => 'success',
                        'message' => 'Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)'
                    ];
                    session()->put($flashKey, $flashMessage);
                    
                    // Debug logging
                    Log::info("Setting flash message for user {$currentUser} with key: {$flashKey}");
                    Log::info("Flash message content: " . json_encode($flashMessage));
                    
                    Log::info("Reviews on Hold restrictions lifted for user #{$currentUser} after first review approval via service");
                }
            }

            // Update reader status
            if ($book) {
                Reader::where('userId', $currentUser)
                    ->where('bookId', $book->id)
                    ->update(['status' => 0]);
            }

            // Create activity records
            $this->createApprovalActivities($review, $book, $currentUser);

            return [
                'success' => true,
                'action' => 'full_approval',
                'message' => 'Review approved successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Auto approve review error: ' . $e->getMessage());

            return [
                'success' => false,
                'action' => 'error',
                'message' => 'Error approving review: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Partially approve a review (VP bonus tokens taken back)
     *
     * @param Review $review
     * @param array $result Optional result from checkReviewExists with review_link
     * @return bool
     */
    private function partiallyApproveReview(Review $review, array $result = null)
    {
        try {
            $book = Book::findOrFail($review->bookId);

            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            } else {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '<=', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            }

            // Safely get user ID with appropriate case sensitivity handling
            $userId = $review->userId ?? $review->userid ?? null;
            if (!$userId) {
                Log::error('Review has no valid userId: ' . $review->id);
                throw new \Exception('Review has no valid userId');
            }

            // Fix case sensitivity in model names (use correct casing)
            $userWallet = Wallet::where('userId', $userId)->first();
            if (!$userWallet) {
                Log::error('Wallet not found for user ID: ' . $userId);
                throw new \Exception('Wallet not found for user');
            }
            $userPoints = $userWallet->currentBalance;

            $authorWallet = Wallet::where('userId', $book->publish_by)->first();
            if (!$authorWallet) {
                Log::error('Wallet not found for author ID: ' . $book->publish_by);
                throw new \Exception('Wallet not found for author');
            }
            $authorPoints = $authorWallet->currentBalance;

            $advertisingPoints = $advertising ? $advertising->bookPrice : 0;

            $vpBonusPoints = SystemControl::where('key_type', 'VP')
                ->where('key', $advertisingPoints)
                ->value('value');

            if (!$vpBonusPoints) {
                Log::warning('VP bonus points not found for advertising points: ' . $advertisingPoints);
                $vpBonusPoints = 0; // Default to 0 if not found
            }

            Log::info("Partial approval: Author ID: {$book->publish_by}, User ID: {$userId}, " .
                     "VP Bonus: {$vpBonusPoints}, Advertising Points: {$advertisingPoints}");

            // Update wallets
            $authorWallet->update([
                'currentBalance' => $authorPoints + $vpBonusPoints
            ]);

            $userWallet->update([
                'currentBalance' => $userPoints - $vpBonusPoints
            ]);

            // Create wallet transaction records
            if ($vpBonusPoints > 0) {
                // Record for author (refund)
                $authorTransaction = new \App\Models\WalletTransaction();
                $authorTransaction->userId = $book->publish_by;
                $authorTransaction->walletId = $authorWallet->id;
                $authorTransaction->depositAmount = $vpBonusPoints;
                $authorTransaction->points = $vpBonusPoints;
                $authorTransaction->reason = "Refund of VP rewards for a partially approved review";
                $authorTransaction->save();

                // Record for reviewer (deduction)
                $userTransaction = new \App\Models\WalletTransaction();
                $userTransaction->userId = $userId;
                $userTransaction->walletId = $userWallet->id;
                $userTransaction->depositAmount = -$vpBonusPoints; // Negative for deduction
                $userTransaction->points = -$vpBonusPoints; // Negative for deduction
                $userTransaction->reason = "Deduction of VP rewards for a partially approved review";
                $userTransaction->save();

                Log::info("Created wallet transaction records for VP bonus points transfer: Author +{$vpBonusPoints}, Reviewer -{$vpBonusPoints}");
            }

            // Update review status including review_link if available
            $updateData = ['reviewStatus' => 2]; // Changed to status 2 for partial approval

            // Include review_link if available from result
            if ($result && !empty($result['review_link'])) {
                $updateData['review_link'] = $result['review_link'];
                Log::info("Saving review link during partial approval: " . $result['review_link']);
            }

            $review->update($updateData);

            // Handle Reviews on Hold logic - unlock features if this is user's first approved review
            $user = User::find($userId);
            if ($user && ($user->waiting_for_first_review_approval ?? false)) {
                // Check if this is their first approved review (including partial validation)
                $approvedReviewsCount = Review::where('userid', $userId)
                    ->whereIn('reviewStatus', [1, 2])
                    ->count();
                
                if ($approvedReviewsCount == 1) { // This is their first approved review
                    // Unlock the user's features and reset tracking flags
                    $user->waiting_for_first_review_approval = false;
                    $user->has_used_first_feature = false;
                    $user->has_taken_first_vault_book = false;
                    $user->save();
                    
                    // Create a congratulatory activity
                    $congratsActivity = new Activity;
                    $congratsActivity->userId = $userId;
                    $congratsActivity->bookId = $review->bookId;
                    $congratsActivity->reason = "Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)";
                    $congratsActivity->save();
                    
                    // Store a flash message for the user to see on their next page visit
                    $flashKey = "user_flash_message_{$userId}";
                    $flashMessage = [
                        'type' => 'success',
                        'message' => 'Congratulations! Your first review got validated, please go to Vault, complete a review and feature your book ;)'
                    ];
                    session()->put($flashKey, $flashMessage);
                    
                    // Debug logging
                    Log::info("Setting flash message for user {$userId} with key: {$flashKey}");
                    Log::info("Flash message content: " . json_encode($flashMessage));
                    Log::info("Reviews on Hold restrictions lifted for user #{$userId} after first partial review approval via service");
                }
            }

            // Create activities - pass the validated userId
            $this->createPartialApprovalActivities($review, $book, $vpBonusPoints);

            Log::info("Partial approval successful for review #{$review->id}. VP bonus: {$vpBonusPoints}");
            return true;
        } catch (\Exception $e) {
            Log::error("Error in partiallyApproveReview: " . $e->getMessage());
            throw $e; // Re-throw to handle in calling function
        }
    }

    /**
     * Create activity records for full approval
     *
     * @param Review $review
     * @param Book $book
     * @param int $userId
     * @return void
     */
    private function createApprovalActivities(Review $review, Book $book, $userId)
    {
        $reviewPublisher = User::findOrFail($book->publish_by);

        $activity = new Activity();
        $activity->userid = $review->publish_by ?? $book->publish_by;
        $activity->bookId = $review->bookId;
        $activity->notification_type = 'author';
        $activity->reason = "One of your books was reviewed. The review has been approved.";
        $activity->save();

        $reviewerActivity = new Activity();
        $reviewerActivity->userid = $userId;
        $reviewerActivity->bookId = $review->bookId;
        $reviewerActivity->notification_type = 'reader';
        $reviewerActivity->reason = "Your review has been approved.";
        $reviewerActivity->read = false;
        $reviewerActivity->save();
    }

    /**
     * Create activity records for partial approval
     *
     * @param Review $review
     * @param Book $book
     * @param int $vpBonusPoints
     * @return void
     */
    private function createPartialApprovalActivities(Review $review, Book $book, $vpBonusPoints)
    {
        $reviewPublisher = User::findOrFail($book->publish_by);

        // Get the reviewer's user ID safely
        $reviewerUserId = $review->userId ?? $review->userid ?? null;

        // If reviewerUserId is still null, try to get it from the review's user relationship
        if (!$reviewerUserId && $review->user) {
            $reviewerUserId = $review->user->id;
        }

        // Log the user ID we found
        Log::info("Creating partial approval notifications with reviewer user ID: {$reviewerUserId}");

        // Check if a similar author notification already exists to prevent duplicates
        $existingAuthorNotification = Activity::where('userid', $book->publish_by)
            ->where('bookId', $review->bookId)
            ->where('reason', 'like', "%{$book->title}%not stamped as a Verified Purchase%")
            ->where('created_at', '>=', now()->subMinutes(5))
            ->first();

        if (!$existingAuthorNotification) {
            // Activity for author
            $authorActivity = new Activity();
            $authorActivity->userid = $book->publish_by;
            $authorActivity->bookId = $review->bookId;
            $authorActivity->notification_type = 'author';
            $authorActivity->reason = "The review of \"{$book->title}\" was published and Approved. However, it was not stamped as a Verified Purchase review on Amazon.com. This is most likely due to a mistake by your reviewer. You spent an additional {$vpBonusPoints} Rewards for the VP option on this review, so we have returned those Rewards to your account.";
            $authorActivity->read = false;
            $authorActivity->save();
            Log::info("Created author notification for partial approval of review #{$review->id}");
        } else {
            Log::info("Skipped creating duplicate author notification for partial approval of review #{$review->id}");
        }

        // Only create reviewer notification if we have a valid user ID
        if ($reviewerUserId) {
            // Check if a similar reviewer notification already exists to prevent duplicates
            $existingReviewerNotification = Activity::where('userid', $reviewerUserId)
                ->where('bookId', $review->bookId)
                ->where('reason', 'like', "%{$book->title}%Approved Partially%")
                ->where('created_at', '>=', now()->subMinutes(5))
                ->first();

            if (!$existingReviewerNotification) {
                // Activity for reader
                $readerActivity = new Activity();
                $readerActivity->userid = $reviewerUserId; // Use the validated user ID
                $readerActivity->bookId = $review->bookId;
                $readerActivity->notification_type = 'reader';
                $readerActivity->reason = "Your review of \"{$book->title}\" was Approved Partially. It was not stamped as Verified Purchase on Amazon for some reason. You received an additional {$vpBonusPoints} Rewards for the VP option on this review, so we have to deduct those Rewards from your account.";
                $readerActivity->read = false;
                $readerActivity->save();

                Log::info("Created reviewer notification for partial approval of review #{$review->id} with userid={$reviewerUserId}");
            } else {
                Log::info("Skipped creating duplicate reviewer notification for partial approval of review #{$review->id}");
            }
        } else {
            Log::error("Could not create reviewer notification: No valid user ID found for review #{$review->id}");
        }
    }

    /**
     * Parse reviews from HTML to find those from a specific reviewer
     *
     * @param string $html
     * @param string $reviewerName
     * @return array
     */
    private function parseReviewsFromHtml($html, $reviewerName)
    {
        try {
            Log::info('Parsing reviews for reviewer: ' . $reviewerName);

            // Normalize reviewer name for comparison (trim spaces)
            $normalizedTargetName = trim($reviewerName);
            Log::info('Looking for exact reviewer name: "' . $normalizedTargetName . '"');

            // Find all profile name spans in the HTML - single standard pattern
            $pattern = '/<span class="a-profile-name">([^<]+)<\/span>/i';

            $results = [];

            if (preg_match_all($pattern, $html, $matches, PREG_OFFSET_CAPTURE)) {
                Log::info('Found ' . count($matches[1]) . ' potential reviewer names');

                // Check each reviewer name for an exact match
                foreach ($matches[1] as $index => $match) {
                    $name = trim($match[0]);
                    $position = $match[1];

                    // Only process exact matches by reviewer name
                    if ($name === $normalizedTargetName) {
                        Log::info('Found exact matching reviewer name: "' . $name . '"');

                        // Get a chunk of HTML around the reviewer name to check for VP badge
                        // Look both before and after the reviewer name to capture the entire review block
                        $beforeSize = 500; // Look back a bit to capture review container start
                        $afterSize = 2500; // Look ahead enough to capture VP badge

                        $startPos = max(0, $position - $beforeSize);
                        $endPos = min(strlen($html), $position + $afterSize);
                        $chunk = substr($html, $startPos, $endPos - $startPos);

                        // Save chunk to a debug log file for inspection if needed
                        if (env('APP_DEBUG', false)) {
                            $debugFilePath = storage_path('logs/review_chunk_' . time() . '_' . mt_rand(1000, 9999) . '.html');
                            file_put_contents($debugFilePath, $chunk);
                            Log::debug("Saved review chunk to $debugFilePath for inspection");
                        }

                        // Modified VP detection to handle multiple patterns
                        $isVerifiedPurchase = false;

                        // Pattern 1: Standard VP badge with data-hook attribute
                        if (preg_match('/<span[^>]*data-hook="avp-badge"[^>]*>.*?Verified Purchase.*?<\/span>/is', $chunk)) {
                            $isVerifiedPurchase = true;
                            Log::info("VP detected with pattern 1 (data-hook=\"avp-badge\")");
                        }
                        // Pattern 2: VP badge with linkless class
                        elseif (preg_match('/<span[^>]*data-hook="avp-badge-linkless"[^>]*>.*?Verified Purchase.*?<\/span>/is', $chunk)) {
                            $isVerifiedPurchase = true;
                            Log::info("VP detected with pattern 2 (data-hook=\"avp-badge-linkless\")");
                        }
                        // Pattern 3: VP badge with anchor and class
                        elseif (preg_match('/<a[^>]*href="[^"]*nodeId=G8UYX7LALQC8V9KA[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-color-state[^"]*">.*?Verified Purchase.*?<\/span>/is', $chunk)) {
                            $isVerifiedPurchase = true;
                            Log::info("VP detected with pattern 3 (a-color-state class with nodeId link)");
                        }
                        // Pattern 4: Check the review-format-strip div which often contains the VP badge
                        elseif (preg_match('/<div[^>]*class="[^"]*review-format-strip[^"]*"[^>]*>.*?Verified Purchase.*?<\/div>/is', $chunk)) {
                            $isVerifiedPurchase = true;
                            Log::info("VP detected with pattern 4 (review-format-strip container)");
                        }
                        // Pattern 5: Any span near the review that contains "Verified Purchase" text (fallback)
                        elseif (preg_match('/<span[^>]*>.*?Verified Purchase.*?<\/span>/is', $chunk)) {
                            $isVerifiedPurchase = true;
                            Log::info("VP detected with pattern 5 (generic span containing 'Verified Purchase')");
                        }

                        Log::info('Is verified purchase: ' . ($isVerifiedPurchase ? 'Yes' : 'No'));

                        // Extract review link if available
                        $reviewLink = null;
                        if (preg_match('/<a[^>]*data-hook="review-title"[^>]*href="([^"]+)"/i', $chunk, $linkMatches)) {
                            $reviewLink = 'https://www.amazon.com' . $linkMatches[1];
                            Log::info('Extracted review link: ' . $reviewLink);
                        }

                        $results[] = [
                            'name' => $name,
                            'verified_purchase' => $isVerifiedPurchase,
                            'review_link' => $reviewLink
                        ];
                    }
                }
            }

            Log::info('Total matched reviews found: ' . count($results));
            return $results;
        } catch (\Exception $e) {
            Log::error('Error parsing reviews from HTML: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return [];
        }
    }
}
