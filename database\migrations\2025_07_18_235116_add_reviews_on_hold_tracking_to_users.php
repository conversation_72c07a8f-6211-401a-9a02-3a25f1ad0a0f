<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('has_used_first_feature')->default(false)->after('waiting_for_first_review_approval');
            $table->boolean('has_taken_first_vault_book')->default(false)->after('has_used_first_feature');
            $table->boolean('show_congratulations_message')->default(false)->after('has_taken_first_vault_book');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['has_used_first_feature', 'has_taken_first_vault_book', 'show_congratulations_message']);
        });
    }
};
