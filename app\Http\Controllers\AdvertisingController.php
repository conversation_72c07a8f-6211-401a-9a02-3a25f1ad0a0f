<?php

namespace App\Http\Controllers;

use App\Http\Controllers\SystemBankController;
use App\Models\Activity;
use App\Models\Advertising;
use App\Models\AdvertisingQueue;
use App\Models\Book;
use App\Models\Review;
use App\Models\SystemControl;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdvertisingController extends Controller
{
    public function boostBook(Request $req)
    {
        // dd($req->all());

        $BookData = Book::where('id', $req->bookId)->first();

        $userWallet = Wallet::where('userId', Auth::user()->id)->first();

        $systemCut = SystemControl::where('key_type', 'commision')->value('value');
        $KindlePoints = SystemControl::where('key_type', 'KU')->value('value');
        $finalPoints = $req->points;

        // Calculate system cut
        $systemCutAmount = round(($finalPoints * $systemCut) / 100, 2);

        // Add system cut to the system bank
        SystemBankController::addSystemCut($systemCutAmount, $req->bookId);

        $freeBoostingPoints = $BookData->BasicPoints;

        if (!$userWallet) {
            return back()->withErrors(['error' => 'User wallet not found']);
        }

        $currentWallet = $userWallet->currentBalance;

        if ($currentWallet < $req->points) {
            return back()->withErrors(['error' => 'Not Enough Points To Boost']);
        } elseif ($currentWallet < $freeBoostingPoints) {
            return back()->withErrors(['error' => "You Don't Have Enough Points To Boost A Book!"]);
        }




        if ($req->KindleUnlimited) {
            $isVpPresendInValut = Advertising::where('bookPrice', '>', '0')->where('bookPrice', '!=', 'KU')->where('bookId', $req->bookId)->where('reeader_required', '>', '0')->first();
            if ($isVpPresendInValut) {
                return back()->withErrors(['error' => 'This Book Is Already Boosted For VP, and One Book Can not be boosted for Verified Purcahse and Kindle Unlimited']);
            }
        }

        if ($req->bookPrice > 0) {
            $isKindleUnlimitedInVault = Advertising::where('bookPrice', 'KU')->where('bookId', $req->bookId)->where('reeader_required', '>', '0')->first();
            if ($isKindleUnlimitedInVault) {
                return back()->withErrors(['error' => 'This Book Is Already Boosted For Kindle Unlimited, and One Book Can not be boosted for Verified Purcahse and Kindle Unlimited']);
            }
        }


        if ($req->KindleUnlimited) {
            $isKindleBookAdvertised = Advertising::where('bookId', $req->bookId)->where('bookPrice', 'KU')->first();
            if ($isKindleBookAdvertised) {
                if ($isKindleBookAdvertised->reeader_required <= 0) {
                    $isKindleBookAdvertised->update([
                        'reeader_required' => "1",
                        "points" => $req->points,
                        'bookPrice' => 'KU',
                        'updated_at' => now()
                    ]);

                    $TotalBalancePoints = $req->points;
                    $newBalance = $currentWallet - $TotalBalancePoints;
                    $userWallet->currentBalance = $newBalance;
                    $userWallet->save();

                    $activity = new Activity;
                    $activity->bookId = $req->bookId;
                    $activity->reason = "You have featured your book";
                    $activity->notification_type = 'author';
                    $activity->save();

                    // Track feature usage for Reviews on Hold system
                    $this->trackFeatureUsage();

                    return back()->withSuccess('Kindle Unlimited Has been boosted');
                } elseif ($isKindleBookAdvertised->reeader_required > 0) {
                    $TotalBalancePoints = $req->points;
                    $newBalance = $currentWallet - $TotalBalancePoints;
                    $userWallet->currentBalance = $newBalance;
                    $userWallet->save();


                    $newBoost = new AdvertisingQueue;
                    $newBoost->points = $TotalBalancePoints;
                    $newBoost->bookId = $req->bookId;
                    $newBoost->require_purchase = $req->has('verifiedPurchase') ? 1 : 0;
                    $newBoost->reeader_required = 1;
                    $newBoost->uid = Auth::user()->id;
                    $newBoost->bookPrice = 'KU';
                    $newBoost->TurnAroundTime = $req->input('turnAroundTime');
                    $newBoost->save();


                    $activity = new Activity;
                    $activity->bookId = $req->bookId;
                    $activity->reason = "You have featured your book";
                    $activity->notification_type = 'author';
                    $activity->save();

                    // Track feature usage for Reviews on Hold system
                    $this->trackFeatureUsage();

                    return back()->withSuccess('Kindle Unlimited Has been Added To Queue');
                }
            }

            $TotalBalancePoints = $req->points;
            $newBalance = $currentWallet - $TotalBalancePoints;
            $userWallet->currentBalance = $newBalance;
            $userWallet->save();

            $newBoost = new Advertising;
            $newBoost->points = $req->points;
            $newBoost->bookId = $req->bookId;
            $newBoost->require_purchase = $req->has('verifiedPurchase') ? 1 : 0;
            $newBoost->reeader_required = 1;
            $newBoost->uid = Auth::user()->id;
            $newBoost->bookPrice = 'KU';
            $newBoost->TurnAroundTime = $req->input('turnAroundTime') ?? 4;
            $newBoost->save();


            $activity = new Activity;
            $activity->bookId = $req->bookId;
            $activity->reason = "You have featured your book";
            $activity->notification_type = 'author';
            $activity->save();

            // Track feature usage for Reviews on Hold system
            $this->trackFeatureUsage();

            return back()->withSuccess('Kindle Unlimited Has been Boosted Now!');
        }



        $existingAdvertise = Advertising::where('bookId', $req->bookId)->first();

        if ($existingAdvertise) {
            if ($req->bookPrice === null) {

                $existingAdvertise = Advertising::where('bookId', $req->bookId)->where('bookPrice', '0')->first();
                if ($existingAdvertise) {
                    if ($existingAdvertise->reeader_required <= 0) {
                        $existingAdvertise->update([
                            'reeader_required' => "1",
                            "points" => $req->points ?? $freeBoostingPoints,
                            'bookPrice' => $req->bookPrice ?? 0,
                            'updated_at' => now()
                        ]);

                        $newBalance = $currentWallet - ($req->points ?? $freeBoostingPoints);
                        $userWallet->currentBalance = $newBalance;
                        $userWallet->save();

                        $activity = new Activity;
                        $activity->bookId = $req->bookId;
                        $activity->reason = "You have featured your book";
                        $activity->notification_type = 'author';
                        $activity->save();

                        // Track feature usage for Reviews on Hold system
                        $this->trackFeatureUsage();

                        return back()->withSuccess('Book Boosted Successfully (No TT)');
                    } elseif ($existingAdvertise->reeader_required > 0) {
                        $newBalance = $currentWallet - ($req->points ?? $freeBoostingPoints);
                        $userWallet->currentBalance = $newBalance;
                        $userWallet->save();

                        $newBoost = new AdvertisingQueue;
                        $newBoost->points = $req->points ?? $freeBoostingPoints;
                        $newBoost->bookId = $req->bookId;
                        $newBoost->require_purchase = $req->has('verifiedPurchase') ? 1 : 0;
                        $newBoost->reeader_required = 1;
                        $newBoost->uid = Auth::user()->id;
                        $newBoost->bookPrice = $req->bookPrice ?? 0;
                        $newBoost->TurnAroundTime = $req->TurnAroundTime ?? 4;
                        $newBoost->save();

                        $activity = new Activity;
                        $activity->bookId = $req->bookId;
                        $activity->reason = "You have featured your book";
                        $activity->notification_type = 'author';
                        $activity->save();

                        // Track feature usage for Reviews on Hold system
                        $this->trackFeatureUsage();

                        return back()->withSuccess('Book Has been Added To Queue (No TT)');
                    }
                }
            } elseif ($req->bookPrice > 0 || $req->bookPrice !== null) {
                if (!$req->input('turnAroundTime')) {
                    return back()->withErrors(['error' => 'Please Select Turnaround time for your book!']);
                }
                $existingAdvertise = Advertising::where('bookId', $req->bookId)->where('bookPrice', '>', '0')->where('bookPrice', '!=', 'KU')->first();
                if ($existingAdvertise) {
                    if ($existingAdvertise->reeader_required <= 0) {
                        $existingAdvertise->update([
                            'reeader_required' => "1",
                            "points" => $finalPoints ?? 0,
                            'bookPrice' => $req->bookPrice,
                            'TurnAroundTime' => $req->input('turnAroundTime'),
                            'updated_at' => now()
                        ]);

                        $newBalance = $currentWallet - $finalPoints;
                        $userWallet->currentBalance = $newBalance;
                        $userWallet->save();

                        $activity = new Activity;
                        $activity->bookId = $req->bookId;
                        $activity->reason = "You have featured your book";
                        $activity->notification_type = 'author';
                        $activity->save();

                        // Track feature usage for Reviews on Hold system
                        $this->trackFeatureUsage();

                        return back()->withSuccess('Book Boosted Successfully (VP)');
                    } elseif ($existingAdvertise->reeader_required > 0) {


                        $newBalance = $currentWallet - $req->points;
                        $userWallet->currentBalance = $newBalance;
                        $userWallet->save();


                        $newBoost = new AdvertisingQueue;
                        $newBoost->points = $finalPoints;
                        $newBoost->bookId = $req->bookId;
                        $newBoost->require_purchase = $req->has('verifiedPurchase') ? 1 : 0;
                        $newBoost->reeader_required = 1;
                        $newBoost->uid = Auth::user()->id;
                        $newBoost->bookPrice = $req->bookPrice ?? 0;
                        $newBoost->TurnAroundTime = $req->input('turnAroundTime');
                        $newBoost->save();


                        $activity = new Activity;
                        $activity->bookId = $req->bookId;
                        $activity->reason = "You have featured your book";
                        $activity->notification_type = 'author';
                        $activity->save();

                        // Track feature usage for Reviews on Hold system
                        $this->trackFeatureUsage();

                        return back()->withSuccess('Book Has been Added To Queue (VP)');
                    }
                }
            }
        }


        if ($req->KindleUnlimited) {
            $MinusTotalBalance = $req->points;
            $newBalance = $currentWallet - $MinusTotalBalance;
        } else {
            $MinusTotalBalance = ($finalPoints ?? $freeBoostingPoints);
            $newBalance = $currentWallet - $MinusTotalBalance;
        }
        $userWallet->currentBalance = $newBalance;
        $userWallet->save();


        $newBoost = new Advertising;
        if ($req->bookPrice == null) {
            $newBoost->points = $req->points ?? $freeBoostingPoints;
        } else {
            $newBoost->points = $req->kindleUnlimited ? $req->points : $finalPoints;
        }
        $newBoost->bookId = $req->bookId;
        $newBoost->require_purchase = $req->has('verifiedPurchase') ? 1 : 0;
        $newBoost->reeader_required = 1;
        $newBoost->uid = Auth::user()->id;
        $newBoost->bookPrice = $req->bookPrice ?? 0;
        $newBoost->TurnAroundTime = $req->input('turnAroundTime') ?? 4;
        $newBoost->save();


        $activity = new Activity;
        $activity->bookId = $req->bookId;
        $activity->reason = "You have featured your book";
        $activity->notification_type = 'author';
        $activity->save();

        // Track feature usage for Reviews on Hold system
        $this->trackFeatureUsage();

        return back()->withSuccess('Book Boosted Successfully');
    }

    /**
     * Track feature usage for Reviews on Hold system
     */
    private function trackFeatureUsage()
    {
        // Check if Reviews on Hold system is enabled
        $reviewsOnHold = SystemControl::where('key_type', 'reviews_on_hold')->first();
        $isReviewsOnHoldEnabled = $reviewsOnHold && $reviewsOnHold->value == 1;
        
        if ($isReviewsOnHoldEnabled) {
            $currentUser = Auth::user();
            
            // Check if user has any approved reviews (including partial validation)
            $hasApprovedReview = Review::where('userid', $currentUser->id)
                ->whereIn('reviewStatus', [1, 2])
                ->exists();
            
            // If user has no approved reviews, track their feature usage
            if (!$hasApprovedReview) {
                if (!$currentUser->has_used_first_feature) {
                    // This is their first feature while having no approved reviews
                    $currentUser->has_used_first_feature = true;
                    $currentUser->save();
                }
            }
        }
    }
}
